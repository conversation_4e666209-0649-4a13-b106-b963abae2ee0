/*----------------------------------------------------------------------
	> File Name: testClock.cpp
	> Author: Jxiepc
	> Mail: Jxiepc
	> Created Time: Sun 24 Jul 2022 02:02:36 PM CST
----------------------------------------------------------------------*/

#include <iostream>
#include <time.h>
#include <sys/time.h>
#include <unistd.h>

using namespace std;

struct UtcTimer
{
    UtcTimer()
    {
        struct timeval tv;
        gettimeofday(&tv, NULL);

        _sys_acc_sec = tv.tv_sec;           // 秒
        _sys_acc_min = _sys_acc_sec / 60;   // 分

        struct tm tm;
        localtime_r((time_t*)&_sys_acc_sec, &tm);
        year = tm.tm_year + 1900;
        mon  = tm.tm_mon + 1;
        day  = tm.tm_mday;
        hour  = tm.tm_hour;
        min  = tm.tm_min;
        sec  = tm.tm_sec;
        reset_utc_fmt();
    }

    uint64_t get_curr_time(int* p_msec = NULL)
    {
        struct timeval tv;

        gettimeofday(&tv, NULL);
        if (p_msec)
            *p_msec = tv.tv_usec / 1000;

        std::cout << tv.tv_sec % 60 << ":" << tv.tv_sec / 60 << std::endl;
        std::cout << sec << ":" << min << std::endl; 
        if ((uint32_t)tv.tv_sec != _sys_acc_sec)
        {
            sec = tv.tv_sec % 60;
            _sys_acc_sec = tv.tv_sec;
            if (_sys_acc_sec / 60 != _sys_acc_min)
            {
                std::cout << "------------------" << std::endl;
                _sys_acc_min = _sys_acc_sec / 60;
                struct tm cur_tm;
                localtime_r((time_t*)&_sys_acc_sec, &cur_tm);
                year = cur_tm.tm_year + 1900;
                mon  = cur_tm.tm_mon + 1;
                day  = cur_tm.tm_mday;
                hour = cur_tm.tm_hour;
                min  = cur_tm.tm_min;
                std::cout << sec << ":" << min << std::endl;
                reset_utc_fmt();
            }
            else
            {
                reset_utc_fmt_sec();
            }
        }

        return tv.tv_sec;
    }

    int year, mon, day, hour, min, sec;
    char utc_fmt[20];

private:
    void reset_utc_fmt()
    {
        snprintf(utc_fmt, 20, "%d-%02d-%02d %02d:%02d:%02d", year, mon, day, hour, min, sec);
    }
    
    void reset_utc_fmt_sec()
    {
        snprintf(utc_fmt + 17, 3, "%02d", sec);
    }

    uint64_t _sys_acc_min;
    uint64_t _sys_acc_sec;
};

struct testTimer {

    testTimer() {
        time_t t = time(0);
        localtime_r(&t, &tm);
    }

    void listen() {
        time_t t = time(0);
        std::cout << t%60 << ":" << t / 60 % 60 << std::endl;
        p();
        if(tm.tm_sec != t % 60) {
            if(tm.tm_min == t / 60 % 60) {
                tm.tm_sec = t % 60;
            }else {
                localtime_r(&t, &tm);
                std::cout << "------------------" << std::endl;
                std::cout << tm.tm_sec << ":" << tm.tm_min << std::endl;
            }
        }
    }

    void p() {
        std::cout << tm.tm_sec << ":" << tm.tm_min << std::endl; 
    }

    struct tm tm;
};

void func1() {
    UtcTimer ut;
    for(int i=0; i<100; ++i) {
        ut.get_curr_time();
        sleep(1);
    }
}

void func5() {
    testTimer ut;
    for(int i=0; i<100; ++i) {
        ut.listen();
        sleep(1);
    }
}

void func2() {
    struct tm tm;
    for(int i=0; i<1000000000; ++i) {
        time_t _sys_acc_sec = time(0);
        localtime_r((time_t*)&_sys_acc_sec, &tm);
    }
}

void func3() {
    for(int i=0; i<1000000000; ++i)
        time(0); 
}

void func4() {
    struct timeval tv;
    for(int i=0; i<1000000000; ++i)
        gettimeofday(&tv, nullptr); 
}

class Timer {
    public:

Timer() {
    time_t t = time(0);
    localtime_r(&t, &tm);
    cout << tm.tm_sec << ":" << tm.tm_min << endl;
    test();
}

void test() {
    sleep(3);
    cout << tm.tm_sec << ":" << tm.tm_min << endl;
}

public:
    struct tm tm;

};

int main(int argc, char* argv[])
{
    clock_t begin = clock();

    //func5();
    clock_t end = clock();
    
	cout << "tick=" << double(end - begin)/1000 << endl;

    cout << sizeof(struct UtcTimer) << endl;
    cout << sizeof(struct Timer) << endl;
    //begin = clock();

    //func2();
    //end = clock();
    //
	//cout << "tick=" << double(end - begin)/1000 << endl;

    //begin = clock();

    //func3();
    //end = clock();
    //
	//cout << "tick=" << double(end - begin)/1000 << endl;


    //begin = clock();

    //func1();
    //end = clock();
    //
	//cout << "tick=" << double(end - begin)/1000 << endl;

    return 0;
}
