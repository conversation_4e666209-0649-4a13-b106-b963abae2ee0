/*----------------------------------------------------------------------
	> File Name: testStat.cpp
	> Author: Jxiepc
	> Mail: Jxiepc
	> Created Time: Mon 25 Jul 2022 04:53:20 PM CST
----------------------------------------------------------------------*/

#include <iostream>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <time.h>
#include <unistd.h>

using namespace std;

static int __lstat(const char* file, struct stat* st = nullptr) {
    struct stat lst;
    int ret = lstat(file, &lst);
    if(st) {
        *st = lst;
    }

    return ret;
}

static int __access(const char* file) {
    
    int ret = access(file, 0);
    return ret;
}


void func1() {
    for(int i=0; i<10000000; ++i)
      __lstat("test");
}

void func2() {
    for(int i=0; i<10000000; ++i)
      __access("test");
}


int main(int argc, char* argv[])
{
//    clock_t begin = clock();
//
//    func1();
//    clock_t end = clock();
//    
//	cout << "tick=" << double(end - begin)/1000 << endl;
//
//    begin = clock();
//
//    func2();
//    end = clock();
//    
//	cout << "tick=" << double(end - begin)/1000 << endl;
    struct stat st;
    int ret = lstat("test_marco.cpp", &st);
    int b = st.st_mtime; 
    cout << st.st_mtime / (1000*3600*24)<< endl;
//    ret = lstat("clent.cpp", &st);
//    int e = st.st_mtime / (1000*3600*24);
//    cout << st.st_mtime / (1000*3600*24)<< endl;
//    double seconds = difftime(time(0), b);
//    cout << seconds / (1000 * 3600 * 24) << endl;
//    cout << time(0) << endl;
    int time_of_day = 24 * 60 * 60;
    cout <<(time(0)-b)/time_of_day << endl;

//    cout << time(0)/(1000 * 3600 * 24) << endl;  

    return 0;
}
