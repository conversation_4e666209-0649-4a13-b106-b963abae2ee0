/*----------------------------------------------------------------------
	> File Name: testMergeSort.cpp
	> Author: Jxiepc
	> Mail: Jxiepc
	> Created Time: Sun 20 Mar 2022 11:02:58 PM CST
----------------------------------------------------------------------*/

#include <vector>
#include <iostream>


void merge(std::vector<int>& vc, int L, int mid, int R) {
    
    int i=0; 
    int l = L;
    int r = mid+1;

    std::vector<int> tmp;
    /* 循环判度胺大小胺 */
    while(l <= mid && r <= R) {
        tmp.push_back(vc[l++] <= vc[r++] ? vc[l++] : vc[r++]); 
    }
    while(l <= mid) {
        tmp.push_back(vc[l++]);
    }

    while(r <= R) {
        tmp.push_back(vc[r++]);
    }

    i=0;
    while(L<=R) {
        vc[L++] = tmp[i++];
    }
}

void solve(std::vector<int>& vc, int L, int R) {
    
    if(L == R)
        return;
    int mid = L + ((R-L) >> 1);
    solve(vc, L, mid);
    solve(vc, mid+1, R);

    merge(vc, L, mid, R);
}

int main(int argc, char* argv[])
{
    std::vector<int> vc = {1,5,4, 2,8,3};
    solve(vc, 0, vc.size()-1);
    for(auto& i:vc) 
        std::cout << i << " ";
    return 0;
}
