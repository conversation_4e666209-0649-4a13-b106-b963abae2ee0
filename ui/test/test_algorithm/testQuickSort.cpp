/*----------------------------------------------------------------------
	> File Name: testQuickSort.cpp
	> Author: Jxiepc
	> Mail: Jxiepc
	> Created Time: Sun 20 Mar 2022 10:41:57 PM CST
----------------------------------------------------------------------*/

#include <iostream>
#include <vector>


void swap(std::vector<int>& vc, int a, int b) {
    vc[a] ^= vc[b];
    vc[b] ^= vc[a];
    vc[a] ^= vc[b];
}

void test(std::vector<int> vc) {
    int len = vc.size();

    for(int i=1; i<=len; ++i) {
        for(int j=i-1; j>=0 && vc[j] > vc[j+1]; --j) {
            swap(vc, j, j+1); 
        }
    }
    for(auto& i : vc) {
        std::cout << i << " ";
    }
    std::cout << std::endl;
}

int main(int argc, char* argv[])
{
    std::vector<int> vc = {1, 5, 3, 6, 9, 2};
    test(vc);
    return 0;
}
