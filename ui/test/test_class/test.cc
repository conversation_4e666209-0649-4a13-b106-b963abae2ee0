#include <iostream>
#include <memory>

using namespace std;


class B;

class A {
public:
    A() {
        cout << "A" << endl;
    }

    ~A() {
        cout << "~A" << endl;
        cout << ptr.use_count() << endl;
    }
    shared_ptr<B> ptr;
};



class B {
public:
    B() {
        cout << "B" << endl;
    }

    ~B() {
        cout << "~B" << endl;
        cout << ptr.use_count() << endl;
    }
    shared_ptr<A> ptr;
};


void test() {
    shared_ptr<A> pa(new A);
    shared_ptr<B> pb(new B);
    
    cout << pa.use_count() << endl;
    cout << pb.use_count() << endl;
//    pa->ptr = pb;
//    pb->ptr = pa;
}

int main() {

    test();

    return 0;
}
