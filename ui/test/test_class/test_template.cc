#include <iostream>
#include <functional>

template<typename T>
class EventListen {
public:
    typedef std::function<void(const T& val)> Func;

    EventListen(T val="", Func event = nullptr)
        : m_val(val), m_cb(event) {}

    void setEvent(Func event) { m_cb = event; }

    EventListen& operator=(T const& newVal) {
        if (newVal != m_val) {
            m_val = newVal;
            m_cb(newVal);
        }
        return *this;
    }

    operator T() const { return m_val; }
private:
    T m_val;
    Func m_cb;
};


int main() {


    return 0;
}
