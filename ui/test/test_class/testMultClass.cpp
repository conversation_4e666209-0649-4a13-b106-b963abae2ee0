/*----------------------------------------------------------------------
	> File Name: testMultClass.cpp
	> Author: Jxiepc
	> Mail: Jxiepc
	> Created Time: Thu 17 Mar 2022 10:29:16 PM CST
----------------------------------------------------------------------*/

#include <iostream>

class One {

public:
    One() {
        std::cout << "One" << std::endl;
    }
};

class Two {

public:
    Two() {
        std::cout << "Two" << std::endl;
    }
};

class Call{

public:
    Call() {
        std::cout << "Call" << std::endl;
    }

    One o;
    Two t;
};


int main(int argc, char* argv[])
{
    Call c;

    return 0;
