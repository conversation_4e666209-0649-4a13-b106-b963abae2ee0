/*----------------------------------------------------------------------
	> File Name: test_valist.cpp
	> Author: Jxiepc
	> Mail: Jxiepc
	> Created Time: Wed 16 Mar 2022 09:43:10 PM CST
----------------------------------------------------------------------*/

#include <iostream>
#include <sstream>
#include <string>
#include <stdarg.h>
#include <stdio.h>

std::stringstream m_ss;
void format(const char *fmt, va_list al){
    char *buf = nullptr;
    int len = vsprintf(buf, fmt, al);
    if (len != -1){
        m_ss << std::string(buf, len);
        free(buf);
    }
}

void format_(const char *fmt, ...){
    va_list al;
    va_start(al, fmt);
    //format(fmt, al);
    va_end(al);
}


void test() {
    int a = 10;
    int b = 20;
   format_("%d %d", a, b); 
   std::cout << m_ss.str() << std::endl;
}

int main(int argc, char* argv[])
{
    std::cout << "---";
    test();
    return 0;
}
