/*----------------------------------------------------------------------
	> File Name: testFile.cpp
	> Author: Jxiepc
	> Mail: Jxiepc
	> Created Time: Mon 25 Jul 2022 10:49:30 AM CST
----------------------------------------------------------------------*/

#include <iostream>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

/**********************
log : 路径名称
Data : 发送数据
LineN: 保留行数
**********************/
int Looplog(char *log,char *Data, int LineN)
{
	char <PERSON>buff[128]="";
	char <PERSON>get[128];
	static char EfileData[128][128];
	static int LineNo=0;
	int LineNum =0;
	sprintf(Ebuff,"%s\n",Data);
	
	FILE *fp;
	fp = fopen(log,"a+");
	while(fgets(Eget,128,fp)!=NULL)
	{
		if(LineNum >(LineN-1))
		{
			memcpy(EfileData+LineNo,Ebuff,128);
			LineNo++;
			if(LineNo >9){LineNo = 0;}
			break;
		}
		if((*Eget == '\n')||(*Eget == '\r'))
		{continue;}
		memset(EfileData+LineNum,0,128);
		memcpy(EfileData+LineNum,Eget,100);
		memset(Eget,0,128);
		LineNum++;
	}
	if((fgets(Eget,128,fp)==NULL)&&(LineNum ==0))
	{
		memcpy(EfileData,Ebuff,100);
		LineNum++;
	}
	else if(LineNum < (LineN+1))
	{
		memcpy(EfileData+LineNum,Ebuff,100);
		LineNum++;
	}
	fclose(fp);
	fp = fopen(log,"w+");
	fclose(fp);
	fp = fopen(log,"a+");
	if(fp == NULL)
	{
		return 0;
	}	
	fseek(fp,0,SEEK_SET);
	
	
	/***/
	for(int y=0;y<LineNum;y++)
	{
		printf(">>>>>>[%d][%s\n",__LINE__,EfileData+y);
	}
	/****/
	
	for(int j = 0;j<LineNum;j++)
	{
		fprintf(fp,"%s",EfileData+j);
	}
	fclose(fp);

    return 0;
}

int main()
{
	int NumNo =0;
	char log[32]="./GUZ.log";
	char Data[128]="";
	int i;
	while(1)
	{		
		i++;
		sprintf(Data,"%d\n",i);
		Looplog	(log,Data,100);	
		sleep(1);			
	}
	return 0;
}



