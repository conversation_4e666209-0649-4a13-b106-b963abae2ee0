/*----------------------------------------------------------------------
	> File Name: testListMoveWin.cpp
	> Author: Jxiepc
	> Mail: Jxiepc
	> Created Time: Sun 20 Mar 2022 10:29:01 PM CST
----------------------------------------------------------------------*/

#include <iostream>
#include <list>
#include <vector>


void test(std::vector<int> vc, int w) {

   /*
    * 准备一个链表当窗口
    * 准备一个vc存储结果
    * */ 
    std::list<int> l;
    std::vector<int> ret;
    ret.resize(vc.size() - w + 1);
    int idx = 0;
    for(int i=0; i<vc.size(); ++i) {
        // 将大的弹出
        while(!l.empty() && vc[l.back()] < vc[i])
            l.pop_back();
        l.push_back(i);

        if(i >= w -1) {
             ret[idx++] = vc[l.front()];
        }
    }
    for(auto& i:ret) {
        std::cout << i << " ";
    }
    std::cout << std::endl;
}

int main(int argc, char* argv[])
{
    std::vector<int> vc = {1, 3, 5, 7, 6, 9, 8};
    test(vc, 3);
    return 0;
}
