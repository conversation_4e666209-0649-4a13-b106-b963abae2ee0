#include <iostream>

namespace Jxiepc {

class Buffer {
public:
        
    Buffer(size_t size);
    void write(std::function<void()> cb);
    void read(std::function<void()> cb);

    size_t getSize() const { return m_size; }
    size_t getUsed() const { return m_used; }
public:
    char* m_buffer;
    size_t m_size;
    size_t m_used;
};

class BufferPool{

public:
    BufferPool(size_t count, size_t size) 
        :head(0), tail(0), m_total(count), m_useNum(0){
            m_bufPool = new Buffer[count];
            for(int i=0; i<count; ++i) {
                m_bufPool[i] = new Buffer[size];
            }
    }

    Buffer* getBuffer() {
        Buffer* buffer = m_bufPool[m_tail];
        m_tail = (m_tail + 1) % m_tatol;
        m_useNUm++;
        if(m_useNum > m_total) {
            m_head = (m_head+1) % m_total;
            m_useNum = m_total;
        }

        return buffer;
    }

    void popBuffer() {
        assert(m_useNum == 0);
        Buffer *buffer = m_buffer[head];
        m_head = (m_head + 1) % m_total;
        m_useNum--;

        return buffer;
    }

private:
    Buffer** m_bufPool;
    int head, tail;
    int m_total, m_useNum;
};

}

