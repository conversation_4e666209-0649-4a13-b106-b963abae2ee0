#include <iostream>
#include <memory>


using namespace std;

class A{

    public:
        A(int a) {}

        ~A() {}
};

void test_shared_ptr() {
	std::shared_ptr<A> ptr1(new A(1));

    auto p1 = ptr1;
	cout << "ptr1引用计数: " << ptr1.use_count() << endl;

	//ptr1.reset();
	//cout << "ptr1引用计数: " << ptr1.use_count() << endl;
	ptr1.reset(new A(2));
	cout << "ptr1引用计数: " << ptr1.use_count() << endl;
}

int main() {
    test_shared_ptr();

    return 0;
}

