/*----------------------------------------------------------------------
	> File Name: clien.cpp
	> Author: Jxiepc
	> Mail: Jxiepc
	> Created Time: Thu 24 Mar 2022 03:18:56 PM CST
----------------------------------------------------------------------*/
#include <sys/types.h>
#include <sys/socket.h>
#include <netdb.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>

#define BUF_SIZE 500

int main(int argc, char *argv[])
{
   struct addrinfo hints;
   struct addrinfo *result, *rp;
   int sfd, s, j;
   size_t len;
   ssize_t nread;
   char buf[BUF_SIZE];

   if (argc < 3) {
       fprintf(stderr, "Usage: %s host port msg...\n", argv[0]);
       exit(EXIT_FAILURE);
   }

   /* 获取匹配主机/端口的地址 */

   memset(&hints, 0, sizeof(struct addrinfo));
   hints.ai_family = AF_UNSPEC;    /* Allow IPv4 or IPv6 */
   hints.ai_socktype = SOCK_DGRAM; /* Datagram socket */
   hints.ai_flags = 0;
   hints.ai_protocol = 0;          /* Any protocol */

   s = getaddrinfo(argv[1], argv[2], &hints, &result);
   if (s != 0) {
       fprintf(stderr, "getaddrinfo: %s\n", gai_strerror(s));
       exit(EXIT_FAILURE);
   }

   /* 返回地址结构的列表。 尝试每个地址，直到我们成功connect
    如果socket或connect失败，我们关闭socket并尝试下一个地址 */

   for (rp = result; rp != NULL; rp = rp->ai_next) {
       sfd = socket(rp->ai_family, rp->ai_socktype,
                    rp->ai_protocol);
       if (sfd == -1)
           continue;

       if (connect(sfd, rp->ai_addr, rp->ai_addrlen) != -1)
           break;                  /* Success */

       close(sfd);
   }

   if (rp == NULL) {               /* No address succeeded */
       fprintf(stderr, "Could not connect\n");
       exit(EXIT_FAILURE);
   }

   freeaddrinfo(result);           /* No longer needed */

   /* 将其余的命令行参数作为单独的数据报发送，并从服务器读取响应   */

   for (j = 3; j < argc; j++) {
       len = strlen(argv[j]) + 1;
               /* +1 for terminating null byte */

       if (len + 1 > BUF_SIZE) {
           fprintf(stderr,
                   "Ignoring long message in argument %d\n", j);
           continue;
       }

       if (write(sfd, argv[j], len) != len) {
           fprintf(stderr, "partial/failed write\n");
           exit(EXIT_FAILURE);
       }

       nread = read(sfd, buf, BUF_SIZE);
       if (nread == -1) {
           perror("read");
           exit(EXIT_FAILURE);
       }

       printf("Received %ld bytes: %s\n", (long) nread, buf);
   }

   exit(EXIT_SUCCESS);
}

