#include <iostream>
#include <vector>

using namespace std;

class Observable;

class Observer {

public:
    virtual ~Observer();
    virtual void update();
    void observer(Observer* s);
    ~Observer();

private:
    Observable* obj;
};

class Observable {
public:
    void register_(Observer* x);
    void unregister(Observer* x);

    void notifyObservers() {
        for(Observer* x:observer_) {
            x->update();
        }
    }
private:
    std::vector<Observer*> observers_;
};

Observer::observer(Observer* s) {
    s->register_(this);
    obj = s;
}

Observer::~Observer {
    obj->unregister(this);
}


int main(int argc, char**) {

    return 0;
}
