#include <utility>      // std::move
#include <iostream>     // std::cout
#include <vector>       // std::vector
#include <string>       // std::string


int main () {
  std::string foo = "foo-string";
  std::string bar = "bar-string";
  std::vector<std::string> myvector;

  myvector.push_back (foo);                    // copies
  
  std::string s = std::move(bar);
    for (std::string& x:myvector) std::cout << ' ' << x;
    std::cout << '\n';
    std::cout << s.c_str() << std::endl;
    std::cout << bar.c_str() << std::endl;

  return 0;
}
