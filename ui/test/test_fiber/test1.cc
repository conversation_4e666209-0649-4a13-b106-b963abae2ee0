#include <stdio.h>
#include <ucontext.h>
#include <iostream>

using namespace std;



ucontext_t ctx[3];

void f1() {
    std::cout << "f1 begin" << std::endl;
    swapcontext(&ctx[1], &ctx[2]);
    std::cout << "f1 end" << std::endl;
}

void f2() {
    std::cout << "f2 begin" << std::endl;

    swapcontext(&ctx[2], &ctx[1]);
    std::cout << "f2 end" << std::endl;
}



int main(int argc, char ** argv) {
    
    char one[1024];
    char t[1024];

    getcontext(&ctx[1]);
    ctx[1].uc_link = nullptr; 
    ctx[1].uc_stack.ss_sp = one;
    ctx[1].uc_stack.ss_size = sizeof(one);
    
    makecontext(&ctx[1], f1, 0);

    getcontext(&ctx[2]);
    ctx[2].uc_link = nullptr; 
    ctx[2].uc_stack.ss_sp = t;
    ctx[2].uc_stack.ss_size = sizeof(t);
    
    makecontext(&ctx[2], f2, 0);

    swapcontext(&ctx[0], &ctx[2]);
    return 0;
}
