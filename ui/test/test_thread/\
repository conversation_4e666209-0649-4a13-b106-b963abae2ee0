#include <thread>
#include <iostream>
#include <unistd.h>

void func1() {
	std::cout << "func1" << std::endl;
	std::cout << "func1 当前线程ID: " << std::this_thread::get_id() << std::endl;
	std::cout << "func1 end" << std::endl; 
}

void func2(int n) {
	std::cout << "func2" << std::endl;
	std::cout << "args: " << n << std::endl; 
	sleep(2);
	std::cout << "fucn2 当前线程ID: " << std::this_thread::get_id() << std::endl;
	std::cout << "func2 end" << std::endl;
}

int main() {
	std::thread t1(func1);
	std::thread t2(func2, 19);
	t1.join();
	t2.detach();
	sleep(5);

    std::cout << "执行完毕: " << t1.get_id() << t2.get_id() << std::endl;

	return 0;
}
