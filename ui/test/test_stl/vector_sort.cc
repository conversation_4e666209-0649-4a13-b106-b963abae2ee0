#include <iostream>


typedef std::pair<int, string> Data;

class Cmp {
public:
    bool operator(const Data& lhs, const Data& rhs) {
        keyless(lhs.first, rhs.first);
    }


    bool operator(const Data::first_type & lhs, const Data& rhs) {
        keyless(lhs, rhs.first);
    }
    bool operator(const Data& lhs, const Data::first_type& rhs) {
        keyless(lhs.first, rhs);
    }

private:
    bool keyLess(const Data::first_type& v1, const Data::first_type& v2) {
        return v1 < v2;
    }
};

int main(int argc, char** argv) {

    return 0;
}
