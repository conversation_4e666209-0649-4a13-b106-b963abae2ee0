/*----------------------------------------------------------------------
	> File Name: test_string.cpp
	> Author: Jxiepc
	> Mail: Jxiepc
	> Created Time: Tue 26 Jul 2022 11:18:08 AM CST
----------------------------------------------------------------------*/

#include <iostream>
#include <string>

using namespace std;

void test() {

    std::string s = "abc.d";
    std::string newFile = s.substr(0, s.rfind('.'));
    //s.insert(s.rfind('.'), "a");
    cout << s.c_str() << endl;
    cout << newFile.c_str() << endl;
}


int main(int argc, char* argv[])
{
    test();
    return 0;
}
