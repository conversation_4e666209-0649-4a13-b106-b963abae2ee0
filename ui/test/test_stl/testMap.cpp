/*----------------------------------------------------------------------
	> File Name: testMap.cpp
	> Author: Jxiepc
	> Mail: Jxiepc
	> Created Time: Wed 27 Jul 2022 05:04:29 PM CST
----------------------------------------------------------------------*/

#include <iostream>
#include <map>
#include <time.h>
#include <unistd.h>

using namespace std;

void test() {
    std::map<uint64_t, int, std::greater<uint64_t>> mp;
    for(int i=0; i<10; ++i) {
        mp[time(0)] = i;
        sleep(1);
    }
    for(auto& i : mp) {
        std::cout << i.first << ":" << i.second << std::endl;
    }
}


int main(int argc, char* argv[])
{
    test();

    return 0;
}
