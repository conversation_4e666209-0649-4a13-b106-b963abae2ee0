/*----------------------------------------------------------------------
	> File Name: testTm.cpp
	> Author: Jxiepc
	> Mail: Jxiepc
	> Created Time: Sun 24 Jul 2022 10:28:22 PM CST
----------------------------------------------------------------------*/

#include <iostream>
#include <time.h>
#include <sys/time.h>
#include <unistd.h>

using namespace std;

struct UtcTimer
{
    UtcTimer()
    {
        struct timeval tv;
        gettimeofday(&tv, NULL);

        _sys_acc_sec = tv.tv_sec;           // 秒
        _sys_acc_min = _sys_acc_sec / 60;   // 分

        struct tm tm;
        localtime_r((time_t*)&_sys_acc_sec, &tm);
        year = tm.tm_year + 1900;
        mon  = tm.tm_mon + 1;
        day  = tm.tm_mday;
        hour  = tm.tm_hour;
        min  = tm.tm_min;
        sec  = tm.tm_sec;
        reset_utc_fmt();
    }

    uint64_t get_curr_time(int* p_msec = NULL)
    {
        struct timeval tv;

        gettimeofday(&tv, NULL);
        if (p_msec)
            *p_msec = tv.tv_usec / 1000;

        if ((uint32_t)tv.tv_sec != _sys_acc_sec)
        {
            sec = tv.tv_sec % 60;
            _sys_acc_sec = tv.tv_sec;
            if (_sys_acc_sec / 60 != _sys_acc_min)
            {
                _sys_acc_min = _sys_acc_sec / 60;
                struct tm cur_tm;
                localtime_r((time_t*)&_sys_acc_sec, &cur_tm);
                year = cur_tm.tm_year + 1900;
                mon  = cur_tm.tm_mon + 1;
                day  = cur_tm.tm_mday;
                hour = cur_tm.tm_hour;
                min  = cur_tm.tm_min;
                reset_utc_fmt();
            }
            else
            {
                reset_utc_fmt_sec();
            }
        }

        return tv.tv_sec;
    }

    int year, mon, day, hour, min, sec;
    char utc_fmt[20];

private:
    void reset_utc_fmt()
    {
        snprintf(utc_fmt, 20, "%d-%02d-%02d %02d:%02d:%02d", year, mon, day, hour, min, sec);
    }
    
    void reset_utc_fmt_sec()
    {
        snprintf(utc_fmt + 17, 3, "%02d", sec);
    }

    uint64_t _sys_acc_min;
    uint64_t _sys_acc_sec;
};

struct testTimer {

    testTimer() {
        time_t t = time(0);
        localtime_r(&t, &tm);
    }

    void listen() {
        time_t t = time(0);
        if(tm.tm_sec != t % 60) {
            if(tm.tm_min == t / 60 % 60) {
                tm.tm_sec = t % 60;
            }else {
                localtime_r(&t, &tm);
            }
        }
    }

    void p() {
        std::cout << tm.tm_sec << ":" << tm.tm_min << std::endl; 
    }

    struct tm tm;
};

void func1() {
    UtcTimer ut;
    for(int i=0; i<100000000; ++i) {
        ut.get_curr_time();
    }
}

void func5() {
    testTimer ut;
    for(int i=0; i<100000000; ++i) {
        ut.listen();
    }
}


int main(int argc, char* argv[])
{
    clock_t begin = clock();

    func1();
    clock_t end = clock();
    
	cout << "tick=" << double(end - begin)/CLOCKS_PER_SEC << "秒" << endl;

    begin = clock();

    func5();
    end = clock();
    
	cout << "tick=" << double(end - begin)/CLOCKS_PER_SEC << "秒"<< endl;

    return 0;
}

