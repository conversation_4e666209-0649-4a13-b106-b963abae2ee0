cmake_minimum_required(VERSION 3.16)
project(lvgl_demo C CXX)

set(CMAKE_CXX_STANDARD 11)
set(CMAKE_OSX_ARCHITECTURES arm64)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)  # 生成compile_commands.json以供IDE使用

add_executable(lvgl_demo
    Jxiepc/main.cc
    # keyboard
    Jxiepc/keyboard.cc
    Jxiepc/keyboard2.cc
    Jxiepc/keyboard_key.cc
    Jxiepc/keyboard_draw.cc
    Jxiepc/keyboard_custom.cc
    Jxiepc/keyboard_lv.cc
    Jxiepc/keyboard_test.cc
    Jxiepc/keyboard_input_number.cc
    Jxiepc/keyboard_input_string.cc
    Jxiepc/keyboard_style.cc
    Jxiepc/keyboard_istring_data.cc

    Jxiepc/event_custom.cc
    Jxiepc/lv_font_Material_18.c
    Jxiepc/base.cc
    Jxiepc/click_ltime_test.cc
    #Jxiepc/map_struct/map.cc
    Jxiepc/map_struct/stu_map.cc
    Jxiepc/lvgl_property.cc
    Jxiepc/event_win.cc
    Jxiepc/event_msgbox.cc
    Jxiepc/data_transform.cc

    #Jxiepc/test_chart.cc
    Jxiepc/charts_drawX.cc
    Jxiepc/charts_cursor.cc
    Jxiepc/charts_scroll.cc
    Jxiepc/chart.cc
    Jxiepc/charts_ticks.cc
    Jxiepc/charts_slid.cc
    Jxiepc/chart_optimized.cc
    Jxiepc/charts_zoom.cc
    Jxiepc/chart_menu.cc
    Jxiepc/charts_scatter.cc
    Jxiepc/chats_new_draw.cc
    Jxiepc/charts_bar.cc


    Jxiepc/util_cal.cc
    Jxiepc/util_win.cc
    Jxiepc/util_roller.cc
    Jxiepc/util_dropdown.cc
    Jxiepc/util_meter.cc
    Jxiepc/util_ring.cc
    Jxiepc/util_line.cc
    Jxiepc/util_canvas.cc
    Jxiepc/util_scale.cc
    # scroll
    Jxiepc/scroll.cc
)

include_directories(${CMAKE_SOURCE_DIR}/lib/include)

# 配置包含路径，支持简化的头文件包含形式
target_include_directories(lvgl_demo PRIVATE
    ${PROJECT_SOURCE_DIR}/Jxiepc                # Jxiepc目录
    ${PROJECT_SOURCE_DIR}                       # 项目根目录
    ${PROJECT_SOURCE_DIR}/lib/include           # 使头文件可以通过 "lvgl/lvgl.h" 形式包含
    /usr/local/include
)

# 添加库文件路径
target_link_directories(lvgl_demo PRIVATE
    ${PROJECT_SOURCE_DIR}/lib
    /usr/local/lib
)

# 链接库
target_link_libraries(lvgl_demo PRIVATE
    lvgl
    lv_drivers
    SDL2
)

# 或者使用 find_package（如果库支持）
find_package(SDL2 REQUIRED)
if(SDL2_FOUND)
    target_include_directories(lvgl_demo PRIVATE ${SDL2_INCLUDE_DIRS})
    target_link_libraries(lvgl_demo PRIVATE ${SDL2_LIBRARIES})
endif()
