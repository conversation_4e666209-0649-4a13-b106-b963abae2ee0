
#include "chart.h"
#include "lvgl/src/core/lv_obj_style.h"
#include <cstdlib>

static void draw_custom_points(lv_event_t *e) {
    lv_obj_draw_part_dsc_t *dsc = lv_event_get_draw_part_dsc(e);
    if (dsc->part != LV_PART_ITEMS) return;

    if (dsc->id == 0) { // 第一个系列（绿色）
        dsc->rect_dsc->radius = 0;
        dsc->rect_dsc->bg_color = lv_palette_main(LV_PALETTE_GREEN);
        dsc->rect_dsc->border_color = lv_palette_main(LV_PALETTE_GREEN);
        dsc->rect_dsc->border_width = 1;
    } else if (dsc->id == 1) { // 第二个系列（红色）
        dsc->rect_dsc->radius = 0;
        dsc->rect_dsc->bg_color = lv_palette_main(LV_PALETTE_RED);
        dsc->rect_dsc->border_color = lv_palette_main(LV_PALETTE_RED);
        dsc->rect_dsc->border_width = 1;
    }
}

void chart_test() {
    // 创建一个可滚动的容器
    lv_obj_t *container = lv_obj_create(lv_scr_act());
    lv_obj_set_size(container, 200, 150); // 容器大小
    lv_obj_align(container, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_scrollbar_mode(container, LV_SCROLLBAR_MODE_AUTO); // 自动显示滚动条
    lv_obj_set_scroll_dir(container, LV_DIR_HOR); // 仅允许水平（X轴）滚动

    // 创建图表
    lv_obj_t *chart = lv_chart_create(container);
    lv_obj_set_size(chart, 400, 120); // 图表宽度大于容器，触发滚动
    lv_obj_align(chart, LV_ALIGN_TOP_LEFT, 0, 0);

    // 设置图表类型为折线图
    lv_chart_set_type(chart, LV_CHART_TYPE_LINE);

    // 设置 X 轴点数（数据点数量）
    lv_chart_set_point_count(chart, 100); // 显示 100 个点

    // 设置 Y 轴范围
    lv_chart_set_range(chart, LV_CHART_AXIS_PRIMARY_Y, 0, 100);

    // 添加数据系列
    lv_chart_series_t *ser = lv_chart_add_series(chart, lv_color_hex(0xFF0000), LV_CHART_AXIS_PRIMARY_Y);

    // 初始化数据（例如，随机值）
    for (int i = 0; i < 100; i++) {
        ser->y_points[i] = (lv_coord_t)(rand() % 100); // 随机生成 0-100 的 Y 值
    }


    lv_obj_add_event_cb(chart, draw_custom_points, LV_EVENT_DRAW_PART_BEGIN, NULL);

    // 刷新图表
    lv_chart_refresh(chart);

    // （可选）动态更新数据以模拟实时滚动
    static int offset = 0;
    lv_timer_t *timer = lv_timer_create(
        [](lv_timer_t *timer) {
            //lv_chart_series_t *ser = (lv_chart_series_t *)timer->user_data;
            //// 移除最旧的数据点，添加新数据点
            //for (int i = 0; i < 99; i++) {
            //    ser->y_points[i] = ser->y_points[i + 1];
            //}
            //ser->y_points[99] = (int)rand() % 100; // 新数据点
            //lv_chart_refresh((lv_obj_t *)timer->user_data); // 刷新图表
            //// 自动滚动到最新数据（可选）
            //lv_obj_scroll_to_x((lv_obj_t *)timer->user_data, (lv_coord_t)(400 - 200), LV_ANIM_ON); // 保持最新数据在容器右侧
        },
        500, // 每 500ms 更新一次
        chart);
}