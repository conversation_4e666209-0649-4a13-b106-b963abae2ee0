#include "chart.h"
#include "lvgl/src/core/lv_event.h"
#include "lvgl/src/core/lv_obj.h"
#include "lvgl/src/extra/widgets/chart/lv_chart.h"
#include <stdio.h>
#include <iostream>
static lv_obj_t * chart1;
static lv_chart_series_t * ser1;
static lv_chart_series_t * ser2;
static lv_chart_cursor_t * cursor;
static lv_obj_t *label;
static uint16_t last_id = 0;
static int show_data_x = 0;

static const char * x_labels[] = {"Jan", "Feb", "Mar", "Apr"};

static void data_event_cb(lv_event_t * e)
{
    //lv_obj_t * obj = lv_event_get_target(e);
    //uint16_t last_id = lv_chart_get_pressed_point(obj);

    //if(e->code == LV_EVENT_VALUE_CHANGED) {
    //    // 游标
    //    last_id = lv_chart_get_pressed_point(obj);
    //    if(last_id != LV_CHART_POINT_NONE) {
    //        lv_chart_set_cursor_point(obj, cursor, NULL, last_id);
    //        
    //        // 准备一个足够大的缓冲区来存储所有系列的数据
    //        char buf[128] = {0};
    //        int offset = snprintf(buf, sizeof(buf), "Point: %d, Values: ", last_id);
    //        
    //        // 遍历所有数据系列
    //        lv_chart_series_t *ser = NULL;
    //        int series_count = 0;
    //        
    //        // 从第一个系列开始遍历
    //        ser = lv_chart_get_series_next(obj, NULL);
    //        while(ser != NULL) {
    //            // 获取当前系列的数据数组
    //            lv_coord_t *data_array = lv_chart_get_y_array(obj, ser);
    //            lv_coord_t value = data_array[last_id];
    //            
    //            // 添加系列值到缓冲区
    //            offset += snprintf(buf + offset, sizeof(buf) - offset, 
    //                              "%s%d", series_count > 0 ? ", " : "", value);
    //            
    //            printf("series_count: %d, value: %d\n", series_count, value);
    //            
    //            // 获取下一个系列
    //            ser = lv_chart_get_series_next(obj, ser);
    //            series_count++;
    //        }
    //        
    //        lv_label_set_text(label, buf);
    //        lv_obj_align_to(label, obj, LV_ALIGN_OUT_TOP_MID, 0, -10);
    //    }
    //}


    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t * chart = lv_event_get_target(e);

    std::cout << "===========" << code << std::endl;

    if(code == LV_EVENT_VALUE_CHANGED) {
        //lv_obj_invalidate(chart);  // 强制重绘图表
        int16_t id = lv_chart_get_pressed_point(chart);
        if(id != LV_CHART_POINT_NONE) {
            // 点击时显示游标
            lv_chart_set_cursor_point(chart, cursor, NULL, id);
            cursor->color.ch.alpha = 255;
            // lv_obj_clear_flag(cursor, LV_OBJ_FLAG_HIDDEN);
        } else {
            cursor->color.ch.alpha = 0;
            // 如果没有选中点，隐藏游标
            // lv_obj_add_flag(cursor, LV_OBJ_FLAG_HIDDEN);
        }
    }
    //if(code == LV_EVENT_REFR_EXT_DRAW_SIZE) {
    //    lv_coord_t * s = (lv_coord_t *)lv_event_get_param(e);
    //    *s = LV_MAX(*s, 20);
    //}
    //else if(code == LV_EVENT_DRAW_POST_END) {
    if(code == LV_EVENT_DRAW_POST_END) {
        
        int32_t id = lv_chart_get_pressed_point(chart);
        
        if(id != LV_CHART_POINT_NONE) {
            last_id = id;
        }
        LV_LOG_USER("Selected point %d", (int)last_id);

        lv_chart_series_t * ser = lv_chart_get_series_next(chart, NULL);
        while(ser) {
            lv_point_t p;
            lv_chart_get_point_pos_by_id(chart, ser, last_id, &p);

            lv_coord_t * y_array = lv_chart_get_y_array(chart, ser);
            lv_coord_t value = y_array[last_id];

            char buf[16];
            lv_snprintf(buf, sizeof(buf), LV_SYMBOL_DUMMY"$%d", value);

            lv_draw_rect_dsc_t draw_rect_dsc;
            lv_draw_rect_dsc_init(&draw_rect_dsc);
            draw_rect_dsc.bg_color = lv_color_black();
            draw_rect_dsc.bg_opa = LV_OPA_50;
            draw_rect_dsc.radius = 3;
            draw_rect_dsc.bg_img_src = buf;
            draw_rect_dsc.bg_img_recolor = lv_color_white();

            lv_area_t a;
            a.x1 = chart->coords.x1 + p.x - 20;
            a.x2 = chart->coords.x1 + p.x + 20;
            a.y1 = chart->coords.y1 + p.y - 30;
            a.y2 = chart->coords.y1 + p.y - 10;

            lv_draw_ctx_t * draw_ctx = lv_event_get_draw_ctx(e);
            lv_draw_rect(draw_ctx, &draw_rect_dsc, &a);

            ser = lv_chart_get_series_next(chart, ser);
        }
    }
    else if(code == LV_EVENT_RELEASED) {
        // lv_obj_invalidate(chart);
        lv_chart_set_cursor_point(chart, cursor, NULL, LV_CHART_POINT_NONE);

    }
}

static void draw_event_cb(lv_event_t * e)
{
    lv_obj_t * obj = lv_event_get_target(e);

    /*Add the faded area before the lines are drawn*/
    lv_obj_draw_part_dsc_t * dsc = lv_event_get_draw_part_dsc(e);

     if(dsc->id == LV_CHART_AXIS_PRIMARY_X && dsc->text) {
        // printf("show_data_x:%d,  %d, text_length: %d, text: %s, value: %d\n", dsc->id,show_data_x, dsc->text_length, dsc->text, dsc->value);
        if(dsc->value < 4) {
            lv_snprintf(dsc->text, dsc->text_length, "%s", x_labels[dsc->value]);
        }
    }

    if(dsc->part == LV_PART_ITEMS) {
        if(!dsc->p1 || !dsc->p2) return;

        /*Add a line mask that keeps the area below the line*/
        lv_draw_mask_line_param_t line_mask_param;
        lv_draw_mask_line_points_init(&line_mask_param, dsc->p1->x, dsc->p1->y, dsc->p2->x, dsc->p2->y,
                                      LV_DRAW_MASK_LINE_SIDE_BOTTOM);
        int16_t line_mask_id = lv_draw_mask_add(&line_mask_param, NULL);

        /*Add a fade effect: transparent bottom covering top*/
        lv_coord_t h = lv_obj_get_height(obj);
        lv_draw_mask_fade_param_t fade_mask_param;
        lv_draw_mask_fade_init(&fade_mask_param, &obj->coords, LV_OPA_COVER, obj->coords.y1 + h / 8, LV_OPA_TRANSP,
                               obj->coords.y2);
        int16_t fade_mask_id = lv_draw_mask_add(&fade_mask_param, NULL);

        /*Draw a rectangle that will be affected by the mask*/
        lv_draw_rect_dsc_t draw_rect_dsc;
        lv_draw_rect_dsc_init(&draw_rect_dsc);
        draw_rect_dsc.bg_opa = LV_OPA_20;
        draw_rect_dsc.bg_color = dsc->line_dsc->color;

        lv_area_t a;
        a.x1 = dsc->p1->x;
        a.x2 = dsc->p2->x - 1;
        a.y1 = LV_MIN(dsc->p1->y, dsc->p2->y);
        a.y2 = obj->coords.y2;
        lv_draw_rect(dsc->draw_ctx, &draw_rect_dsc, &a);

        /*Remove the masks*/
        lv_draw_mask_free_param(&line_mask_param);
        lv_draw_mask_free_param(&fade_mask_param);
        lv_draw_mask_remove_id(line_mask_id);
        lv_draw_mask_remove_id(fade_mask_id);
    }
    /*Hook the division lines too*/
    else if(dsc->part == LV_PART_MAIN) {
        if(dsc->line_dsc == NULL || dsc->p1 == NULL || dsc->p2 == NULL) return;

        /*Vertical line*/
        if(dsc->p1->x == dsc->p2->x) {
            dsc->line_dsc->color  = lv_palette_lighten(LV_PALETTE_GREY, 1);
            if(dsc->id == 3) {
                dsc->line_dsc->width  = 2;
                dsc->line_dsc->dash_gap  = 0;
                dsc->line_dsc->dash_width  = 0;
            }
            else {
                dsc->line_dsc->width = 1;
                dsc->line_dsc->dash_gap  = 6;
                dsc->line_dsc->dash_width  = 6;
            }
        }
        /*Horizontal line*/
        else {
            if(dsc->id == 2) {
                dsc->line_dsc->width  = 2;
                dsc->line_dsc->dash_gap  = 0;
                dsc->line_dsc->dash_width  = 0;
            }
            else {
                dsc->line_dsc->width = 2;
                dsc->line_dsc->dash_gap  = 6;
                dsc->line_dsc->dash_width  = 6;
            }

            if(dsc->id == 1  || dsc->id == 3) {
                dsc->line_dsc->color  = lv_palette_main(LV_PALETTE_GREEN);
            }
            else {
                dsc->line_dsc->color  = lv_palette_lighten(LV_PALETTE_GREY, 1);
            }
        }
    }
    // 处理坐标轴标签的绘制
    else if(dsc->part == LV_PART_TICKS && dsc->text) {
        // 强调重要的标签
        if(dsc->value % 20 == 0 || dsc->value == 50) {
            dsc->label_dsc->color = lv_palette_main(LV_PALETTE_RED);
            dsc->label_dsc->font = LV_FONT_DEFAULT;
        }
    }
}

static void add_data(lv_timer_t * timer)
{
    LV_UNUSED(timer);
    static uint32_t cnt = 0;
    //show_data_x = lv_rand(20, 90);
    //lv_chart_set_next_value(chart1, ser1, show_data_x);
    uint16_t point_count = lv_chart_get_point_count(chart1);
    lv_chart_set_value_by_id(chart1, ser1, 1, lv_rand(20, 90));
    // lv_chart_set_next_value(chart1, ser1, lv_rand(20, 90));
    // lv_obj_set_style_bg_opa(cursor, LV_OPA_0, LV_PART_CURSOR);
    //if(cnt % 4 == 0) lv_chart_set_next_value(chart1, ser2, lv_rand(40, 60));
    lv_chart_refresh(chart1);

    cnt++;
}

void set_style(lv_obj_t * obj) {

    
    lv_obj_set_style_bg_color(obj, lv_palette_main(LV_PALETTE_BLUE), 0);

    /// 设置外边框
    lv_obj_set_style_border_width(obj, 10, 0);
    lv_obj_set_style_border_color(obj, lv_palette_main(LV_PALETTE_RED), 0);
    lv_obj_set_style_border_side(obj, LV_BORDER_SIDE_BOTTOM, 0);

    /// 设置内边框
    lv_obj_set_style_border_width(obj, 10, 0);
    lv_obj_set_style_border_color(obj, lv_palette_main(LV_PALETTE_RED), 0);
    lv_obj_set_style_border_side(obj, LV_BORDER_SIDE_BOTTOM, 0);
    
    ///// 阴影宽度
    //lv_obj_set_style_shadow_width(obj, 10, LV_PART_MAIN);
    ///// 阴影扩散
    //lv_obj_set_style_shadow_spread(obj, 10, LV_PART_MAIN);
    ///// 阴影偏移
    //lv_obj_set_style_shadow_ofs_x(obj, 10, LV_PART_MAIN);
    //lv_obj_set_style_shadow_ofs_y(obj, 10, LV_PART_MAIN);
    ///// 阴影颜色
    //lv_obj_set_style_shadow_color(        obj, lv_palette_main(LV_PALETTE_RED),LV_PART_MAIN);
}

void chart_draw_x() {
    /*Create a chart1*/
    chart1 = lv_chart_create(lv_scr_act());
    lv_obj_set_size(chart1, 300, 220);
    lv_obj_center(chart1);
    lv_obj_set_style_pad_all(chart1, 20, 0); // 增加内边距，为坐标轴刻度留出空间
    //set_style(chart1);
    
    // 设置图表类型和属性
    lv_chart_set_type(chart1, LV_CHART_TYPE_LINE);
    // lv_chart_set_point_count(chart1, 10);
    
    // 启用网格线和刻度
    lv_chart_set_div_line_count(chart1, 10, 20); // X轴和Y轴的分割线数量
    /* 创建虚线样式 */

    // 设置X轴和Y轴的显示
    // lv_obj_add_flag(chart1, LV_OBJ_FLAG_CLICKABLE);
    
    // 添加事件回调
    lv_obj_add_event_cb(chart1, draw_event_cb, LV_EVENT_DRAW_PART_BEGIN, NULL);
    lv_obj_add_event_cb(chart1, data_event_cb, LV_EVENT_ALL, NULL);
    lv_chart_set_update_mode(chart1, LV_CHART_UPDATE_MODE_SHIFT);

    /*Add two data series*/
    ser1 = lv_chart_add_series(chart1, lv_palette_main(LV_PALETTE_RED), LV_CHART_AXIS_PRIMARY_Y);
    ser2 = lv_chart_add_series(chart1, lv_palette_main(LV_PALETTE_BLUE), LV_CHART_AXIS_SECONDARY_Y); // 使用辅助Y轴以区分

    // lv_chart_set_zoom_x(chart1, 257);

    // 设置Y轴范围
    lv_chart_set_range(chart1, LV_CHART_AXIS_PRIMARY_Y, 0, 100);
    lv_chart_set_range(chart1, LV_CHART_AXIS_PRIMARY_X, 0, 100);

    // 添加游标
    cursor = lv_chart_add_cursor(chart1, lv_palette_main(LV_PALETTE_BLUE), LV_DIR_BOTTOM | LV_DIR_TOP);
    // 设置游标线宽度
    lv_obj_set_style_line_width(chart1, 10, LV_PART_CURSOR);

    
    // 关键部分：设置X轴和Y轴的刻度
    // 参数：图表对象，轴，主刻度线宽，次刻度线宽，主刻度数量，次刻度数量，显示标签，文本长度
    lv_chart_set_axis_tick(chart1, LV_CHART_AXIS_PRIMARY_X, 20, 5, 10, 2, true, 10);
    lv_chart_set_axis_tick(chart1, LV_CHART_AXIS_PRIMARY_Y, 20, 5, 5, 5, false, 50);
    lv_obj_add_flag(chart1, LV_OBJ_FLAG_SCROLL_CHAIN_HOR);

    //static lv_style_t style_dashed;
    //lv_style_init(&style_dashed);
    //lv_style_set_line_width(&style_dashed, 2); /* 线条宽度 */
    //lv_style_set_line_color(&style_dashed, lv_color_hex(0xFF0000)); /* 线条颜色 */
    //lv_style_set_line_dash_width(&style_dashed, 6); /* 虚线段长度 */
    //lv_style_set_line_dash_gap(&style_dashed, 4); /* 虚线间隙长度 */
    
    /* 应用虚线样式到数据系列 */
    //lv_obj_add_style(chart1, &style_dashed, LV_PART_ITEMS);
    
    
    // LVGL 8.4中设置自定义轴标签
    
    // 填充初始数据
    uint32_t i;
    for(i = 0; i < 20; i++) {
        lv_chart_set_next_value(chart1, ser1, lv_rand(20, 90));
        lv_chart_set_next_value(chart1, ser2, lv_rand(30, 70));
    }

    label = lv_label_create(lv_scr_act());
    lv_obj_align_to(label, chart1, LV_ALIGN_OUT_TOP_MID, 0, -10);


     lv_timer_create(add_data, 20000, NULL);
}
