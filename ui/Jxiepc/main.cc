#include "lvgl/lvgl.h"
#include "lv_drivers/sdl/sdl.h"
#include "lvgl/src/draw/lv_draw_mask.h"
#include "lvgl/src/extra/widgets/chart/lv_chart.h"
#include "lvgl/src/misc/lv_color.h"
#include "scroll.h"
#include "data_transform.h"
#include "util.h"
#include "keyboard.h"
#include "map_struct/stu_map.h"
#include "event.h"
#include "map_struct/map.h"
#include "event_test.h"
#include "base.h"
#include "chart.h"
#include "lvgl_property.h"
#include <unistd.h>
#include <stdio.h>
#include <time.h>

#define DISPLAY_WIDTH 1000
#define DISPLAY_HEIGHT 800

lv_chart_series_t * ser;
lv_obj_t * chart;

void test_draw() {
    lv_area_t rect_area = { .x1 = 50, .y1 = 50, .x2 = 150, .y2 = 100 };
    lv_draw_rect_dsc_t rect_dsc;
    lv_draw_rect_dsc_init(&rect_dsc);
    rect_dsc.bg_color = lv_color_hex(0xFF0000); // 红色背景
    rect_dsc.bg_opa = LV_OPA_COVER;

    // 创建淡入淡出遮罩
    lv_draw_mask_fade_param_t fade_mask_param;
    lv_draw_mask_fade_init(&fade_mask_param, &rect_area, LV_OPA_COVER, 50, LV_OPA_TRANSP, 100);
    int16_t mask_id = lv_draw_mask_add(&fade_mask_param, NULL);

    // 绘制矩形
    lv_draw_rect(NULL, &rect_dsc, &rect_area);

    // 移除遮罩
    lv_draw_mask_remove_id(mask_id);    
}

void test_keyboard() {
//     lv_example_simple_gui();
    // lv_ex_btnmatrix_1();
    // lv_ex_btnmatrix_2();
    // lv_ex_btnmatrix_3(indev);
    // lv_ex_btnmatrix_test();
//     lv_event_custom();

    // lv_ex_btnmatrix_custom();
    // lv_ex_btnmatrix_lv();

    // lv_ex_btnmatrix_test();
    // lv_ex_btnmatrix_lv();
    // lv_ex_btnmatrix_style();
    // lv_ex_btnmatrix_string();
    // lv_ex_btnmatrix_data();
}

void test_util() {
    // test_calendar();
    // test_roller();
    // test_dropdown();
    // test_meter();
    // test_canvas();
    test_line();
    // test_ring();
    // test_canvas();
    // test_win();
}

void test_chart() {
    // chart_test();
    // chart_optimized();
    // chart_scroll();
    // chart_new_draw();
    // chart_zoom();
    //chart_draw_optimized();
    // chart_draw_x();
    // chart_draw_cursor();
    // chart_tick();
    // chart_draw_slid();
    // chart_menu();
    // test_bar();
}

void test_event() {
  // test_click_long_time();
  // test_view();
  // lv_event_win();
  // lv_event_msgbox();
  // lv_event_win_page();
  // lv_example_simple_gui();
  // test_chart();
  // test_draw();
  // create_fan_animation();
    test_util();
  // scroll_test();

  //lv_example_chart_next_value2();
}

void test_lvgl() {
    lv_init();

    /* 初始化 SDL 显示驱动 */
    sdl_init();

    /* 创建显示缓冲区 */
    static lv_color_t buf1[SDL_HOR_RES * 100];
    static lv_disp_draw_buf_t draw_buf;
    lv_disp_draw_buf_init(&draw_buf, buf1, NULL, SDL_HOR_RES * 100);

    /* 初始化显示驱动 */
    static lv_disp_drv_t disp_drv;
    lv_disp_drv_init(&disp_drv);
    disp_drv.flush_cb = sdl_display_flush;
    disp_drv.draw_buf = &draw_buf;
    disp_drv.hor_res = SDL_HOR_RES;
    disp_drv.ver_res = SDL_VER_RES;
    lv_disp_drv_register(&disp_drv);

    /* 初始化输入设备（鼠标） */
    lv_indev_drv_t indev_drv;
    lv_indev_drv_init(&indev_drv);
    indev_drv.type = LV_INDEV_TYPE_POINTER;
    indev_drv.read_cb = sdl_mouse_read;
    lv_indev_t* indev = lv_indev_drv_register(&indev_drv);

    /* 创建你的界面 */
    test_event();
    /* 主循环 */
    while (1)
    {
        lv_timer_handler(); // 处理 LVGL 任务
        lv_tick_inc(5);
        usleep(5 * 1000);   // 延时 5 ms
    }

}

void test_other(person_map_t *map) {

    //int_map_t *map = map_new();
    //map_add(map, 1, 1);
    //map_add(map, 1, 2);
    //map_add(map, 1, 3);
    //map_add(map, 1, 4);
    Person p;
    p.age = 1;
    // p.name = "张三";
    map_add(map, 1, &p);
}

int main(void)
{
    test_lvgl();
    //uint8_t u8 = 42;
    //float f = 3.14f;
    //const char* str = "123.456";

    //double d1 = to_double(&u8, DATA_ALL_TYPE_UINT8);
    //double d2 = to_double(&f, DATA_ALL_TYPE_FLOAT);
    //double d3 = to_double((void*)str, DATA_ALL_TYPE_STRING);

    //printf("u8 to double: %f\n", d1);     // 输出：42.000000
    //printf("float to double: %f\n", d2);  // 输出：3.140000
    //printf("string to double: %f\n", d3); // 输出：123.456000

    return 0;
}

