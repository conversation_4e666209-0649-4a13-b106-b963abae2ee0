#include "event.h"
#include <unistd.h>


#define MY_CUSTOM_EVENT LV_EVENT_USER_1


uint32_t id = lv_event_register_id();

static void my_event_cb(lv_event_t * e) {
    if (lv_event_get_code(e) == id) {
        int param = (int)(intptr_t)lv_event_get_param(e);
        if (param) {
            std::cout << "Received event: value=" << std::endl;
        }
    }
}


void lv_event_custom() {
    lv_obj_t *btn = lv_btn_create(lv_scr_act());
    lv_obj_add_event_cb(btn, my_event_cb, (lv_event_code_t)id, NULL);
    int v = 41;

    sleep(2);
    lv_event_send(btn, (lv_event_code_t)id, (void*)(intptr_t)&v);
    
}
