#include "lvgl/src/extra/widgets/meter/lv_meter.h"
#include "lvgl/src/misc/lv_area.h"
#include "util.h"

#include <iostream>

static lv_obj_t * meter;

static void meter_draw_event_cb(lv_event_t * e) {
    // 设置轴心
    lv_obj_t * meter = lv_event_get_target(e);
    lv_event_code_t code = lv_event_get_code(e);
    lv_draw_ctx_t * draw_ctx = lv_event_get_draw_ctx(e);

    if (code == LV_EVENT_DRAW_MAIN) {

        // 获取仪表中心
        lv_coord_t center_x = meter->coords.x1 + lv_obj_get_width(meter) / 2;
        lv_coord_t center_y = meter->coords.y1 + lv_obj_get_height(meter) / 2;

        // 定义轴心样式
        //  lv_draw_rect_dsc_t rect_dsc;
        //  lv_draw_rect_dsc_init(&rect_dsc);
        //  rect_dsc.radius = LV_RADIUS_CIRCLE; // 圆形
        //  rect_dsc.bg_color = lv_color_hex(0xFF0000); // 红色背景
        //  rect_dsc.bg_opa = LV_OPA_100; // 完全不透明
        //  rect_dsc.border_color = lv_color_hex(0x000000); // 黑色边框
        //  rect_dsc.border_width = 2;
        //  rect_dsc.border_opa = LV_OPA_100;

        // // 绘制轴心圆（半径 10 像素）
        // lv_coord_t radius = 50;
        lv_area_t pivot_area;
        // pivot_area.x1 = center_x - radius;
        // pivot_area.y1 = center_y - radius;
        // pivot_area.x2 = center_x + radius;
        // pivot_area.y2 = center_y + radius;
        // lv_draw_rect(draw_ctx, &rect_dsc, &pivot_area);
        lv_obj_draw_part_dsc_t *dsc = lv_event_get_draw_part_dsc(e);
        
        dsc->rect_dsc->radius = LV_RADIUS_CIRCLE; // 圆形
        dsc->rect_dsc->bg_color = lv_color_hex(0xFF0000); // 红色背景
        dsc->rect_dsc->bg_opa = LV_OPA_100; // 完全不透明
        dsc->rect_dsc->border_color = lv_color_hex(0x000000); // 黑色边框
        dsc->rect_dsc->border_width = 2;
        dsc->rect_dsc->border_opa = LV_OPA_100;

        lv_coord_t radius = 50;
        dsc->draw_area->x1 = center_x - radius;
        dsc->draw_area->y1 = center_y - radius;
        dsc->draw_area->x2 = center_x + radius;
        dsc->draw_area->y2 = center_y + radius;
        // lv_draw_rect(dsc->draw_ctx, dsc->rect_dsc, dsc->draw_area);
    }
}


static void set_value(void * indic, int32_t v)
{
    lv_meter_set_indicator_value(meter, (lv_meter_indicator_t *)indic, v);
}

static void meter_event_cb(lv_event_t * e)
{
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t * obj = lv_event_get_target(e);
    lv_obj_draw_part_dsc_t *dsc = lv_event_get_draw_part_dsc(e);
    if(dsc->type == LV_METER_DRAW_PART_ARC) {
        std::cout << "================" << std::endl;
        // 弧形指示器的半径、宽度、颜色
        dsc->radius = 100;
        if(dsc->arc_dsc != NULL) {
            dsc->arc_dsc->color = lv_color_hex(0xFF0000);
            dsc->arc_dsc->width= 10;
        }
    }else if(dsc->type == LV_METER_DRAW_PART_NEEDLE_LINE) {
        if(dsc->line_dsc != NULL) {
            dsc->line_dsc->color = lv_color_hex(0x0000FF);
            dsc->line_dsc->width = 20;
            dsc->radius = 20;
            lv_point_t hor_p1_new = *dsc->p2;

            hor_p1_new.x += 20;

            *((lv_point_t *)dsc->p2) = hor_p1_new;
        }
    }else if(dsc->type == LV_METER_DRAW_PART_TICK){
        // std::cout << "Tick value: " << dsc->value << " text: " << dsc->text << " id: " << dsc->id << std::endl;
        // if(dsc->label_dsc != NULL) {
        //     dsc->label_dsc->color = lv_color_hex(0xFF0000);
        // }

        if(dsc->line_dsc != NULL) {
            dsc->line_dsc->color = lv_color_hex(0x0000FF);
            dsc->radius = 1;
        }

    }
}

/**
 * A simple meter
 */
void test_meter(void)
{
    meter = lv_meter_create(lv_scr_act());
    lv_obj_center(meter);
    lv_obj_set_size(meter, 200, 200);

    /*Add a scale first*/
    lv_meter_scale_t * scale = lv_meter_add_scale(meter);
    // lv_meter_set_scale_ticks(meter, scale, 20, 2, 10, lv_palette_main(LV_PALETTE_GREY));
    // lv_meter_set_scale_major_ticks(meter, scale, 10, 4, 20, lv_color_black(), 10);
    lv_meter_set_scale_ticks(meter, scale, 41, 2, 10, lv_palette_main(LV_PALETTE_GREY));
    lv_meter_set_scale_major_ticks(meter, scale, 4, 4, 20, lv_color_black(), 0);
    lv_meter_set_scale_range(meter, scale, 0, 100, 270, 90);

    lv_meter_indicator_t * indic;
    // /*Add a blue arc to the start*/
    // indic = lv_meter_add_arc(meter, scale, 3, lv_palette_main(LV_PALETTE_BLUE), -100);
    // lv_meter_set_indicator_start_value(meter, indic, 0);
    // lv_meter_set_indicator_end_value(meter, indic, 20);

    // /*Make the tick lines blue at the start of the scale*/
    // indic = lv_meter_add_scale_lines(meter, scale, lv_palette_main(LV_PALETTE_BLUE), lv_palette_main(LV_PALETTE_BLUE),
    //                                  true, 20);
    // // lv_meter_set_indicator_value(meter, indic, 50);
    // lv_meter_set_indicator_start_value(meter, indic, 0);
    // lv_meter_set_indicator_end_value(meter, indic, 20);

    // // /*Add a red arc to the end*/
    // indic = lv_meter_add_arc(meter, scale, 3, lv_palette_main(LV_PALETTE_RED), 0);
    // lv_meter_set_indicator_start_value(meter, indic, 80);
    // lv_meter_set_indicator_end_value(meter, indic, 100);

    // /*Make the tick lines red at the end of the scale*/
    // indic = lv_meter_add_scale_lines(meter, scale, lv_palette_main(LV_PALETTE_RED), lv_palette_main(LV_PALETTE_RED), false,
    //                                  10);
    // lv_meter_set_indicator_start_value(meter, indic, 10);
    // lv_meter_set_indicator_end_value(meter, indic, 20);


    // 设置下限
    indic = lv_meter_add_arc(
        meter, scale, 2,
        // urq_get_color(project->env.theme_color, &conf->lower_limit_color),
        lv_color_make(
            255,
            0,
            0),
        10);
    lv_meter_set_indicator_start_value(meter, indic, 0);
    lv_meter_set_indicator_end_value(meter, indic, 20);


    // 设置中间
    indic = lv_meter_add_arc(
        meter, scale, 2,
        // urq_get_color(project->env.theme_color, &conf->middle_color),
        lv_color_make(
            0,
            255,
            0),
        10);
    lv_meter_set_indicator_start_value(meter, indic, 20);
    lv_meter_set_indicator_end_value(meter, indic, 40);

    // 设置上限
    indic = lv_meter_add_arc(
        meter, scale, 2,
        // urq_get_color(project->env.theme_color, &conf->upper_limit_color),
        lv_color_make(
            0,
            0,
            255),
        10);
    lv_meter_set_indicator_start_value(meter, indic, 40);
    lv_meter_set_indicator_end_value(
        meter, indic, 60);

    // /*Add a needle line indicator*/
    indic = lv_meter_add_needle_line(meter, scale, 4, lv_palette_main(LV_PALETTE_GREY), 0);
    lv_meter_set_indicator_value(meter, (lv_meter_indicator_t *)indic,0);

    // lv_obj_add_event_cb(meter, meter_event_cb, LV_EVENT_DRAW_PART_BEGIN, NULL);
    lv_obj_add_event_cb(meter, meter_draw_event_cb, LV_EVENT_DRAW_MAIN, NULL);

    /*Create an animation to set the value*/
    // lv_anim_t a;
    // lv_anim_init(&a);
    // lv_anim_set_exec_cb(&a, set_value);
    // lv_anim_set_var(&a, indic);
    // lv_anim_set_values(&a, 0, 100);
    // lv_anim_set_time(&a, 2000);
    // lv_anim_set_repeat_delay(&a, 100);
    // lv_anim_set_playback_time(&a, 500);
    // lv_anim_set_playback_delay(&a, 100);
    // lv_anim_set_repeat_count(&a, LV_ANIM_REPEAT_INFINITE);
    // lv_anim_start(&a);
}
