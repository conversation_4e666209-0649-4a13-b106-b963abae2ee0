/*******************************************************************************
 * Size: 18 px
 * Bpp: 4
 * Opts: --bpp 4 --size 18 --no-compress --font MaterialSymbolsOutlined[FILL,GRAD,opsz,wght].ttf --range 62802 --format lvgl -o lv_font_Material_18.c
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl/lvgl.h"
#endif

#ifndef LV_FONT_MATERIAL_18
#define LV_FONT_MATERIAL_18 1
#endif

#if LV_FONT_MATERIAL_18

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+F552 "" */
    0x2d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0x7f, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0xf7,
    0x8f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf8,
    0x8f, 0x1, 0x33, 0x32, 0x0, 0x22, 0x0, 0xf8,
    0x8f, 0xd, 0xef, 0xef, 0x0, 0x88, 0x0, 0xf8,
    0x8f, 0xd, 0x3f, 0x3f, 0x8, 0x99, 0x80, 0xf8,
    0x8f, 0xd, 0x3f, 0x3f, 0xb, 0xff, 0xb0, 0xf8,
    0x8f, 0xd, 0x31, 0x2f, 0x0, 0xbb, 0x0, 0xf8,
    0x8f, 0x3, 0x0, 0x3, 0x0, 0x0, 0x0, 0xf8,
    0x8f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf8,
    0x7f, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0xf7,
    0x2d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 288, .box_w = 16, .box_h = 12, .ofs_x = 1, .ofs_y = 3}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/



/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 62802, .range_length = 1, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 1,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_Material_18 = {
#else
lv_font_t lv_font_Material_18 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 12,          /*The maximum line height required by the font*/
    .base_line = -3,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = 0,
    .underline_thickness = 0,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_MATERIAL_18*/

