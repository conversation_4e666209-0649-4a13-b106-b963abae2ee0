#include "keyboard.h"
#include <stdio.h>

//lv_obj_t * btnm1;
//static const char * btnm1_map[] = { "    "LV_SYMBOL_AUDIO"\n#ff0000 AUDIO#", "    "LV_SYMBOL_VIDEO"\n#ff0000 VIDEO#", "\n",//换行
//								   "    "LV_SYMBOL_HOME"\n#ff0000 HOME#",   "    "LV_SYMBOL_SAVE"\n#ff0000 SAVE#",
//								   "" };//空字符串作为结束符
//static const char * btnm2_map[] = { "Btn1","Btn2","Btn3","" };
//
//
//static void event_handler(lv_event_t * e)
//{
//    lv_event_code_t code = lv_event_get_code(e);
//    lv_obj_t * obj = lv_event_get_target(e);
//    const char * txt;
//    uint16_t btn_id;
//
//    if(code == LV_EVENT_VALUE_CHANGED) {
//        // 方法1：获取当前被点击按钮 id
//        btn_id = lv_btnmatrix_get_selected_btn(obj);
//
//        // 方法2（不建议）：event data 已不再推荐使用直接强转
//        // btn_id = *((uint16_t*)lv_event_get_param(e)); // 旧版本方式，8.x 已废弃
//
//        // 获取当前按钮的文本
//        txt = lv_btnmatrix_get_btn_text(obj, btn_id);
//        printf("text:%s was pressed id:%d \n", txt, btn_id);
//
//        //// 切换 btnm1 中 id=0 的禁用属性
//        //if (strcmp(txt, btnm2_map[0]) == 0)
//        //{
//        //    if (lv_btnmatrix_get_btn_ctrl(btnm1, 0, LV_BTNMATRIX_CTRL_DISABLED))
//        //        lv_btnmatrix_clear_btn_ctrl(btnm1, 0, LV_BTNMATRIX_CTRL_DISABLED);
//        //    else
//        //        lv_btnmatrix_set_btn_ctrl(btnm1, 0, LV_BTNMATRIX_CTRL_DISABLED);
//        //}
//
//        //// 切换 btnm1 中 id=1 的隐藏属性
//        //if (strcmp(txt, btnm2_map[1]) == 0)
//        //{
//        //    if (lv_btnmatrix_get_btn_ctrl(btnm1, 1, LV_BTNMATRIX_CTRL_HIDDEN))
//        //        lv_btnmatrix_clear_btn_ctrl(btnm1, 1, LV_BTNMATRIX_CTRL_HIDDEN);
//        //    else
//        //        lv_btnmatrix_set_btn_ctrl(btnm1, 1, LV_BTNMATRIX_CTRL_HIDDEN);
//        //}
//    }
//}
//

void lv_ex_btnmatrix_2(void)
{
    std::cout << "xx" << std::endl;
    // static lv_style_t style_btnm1_bg;
    // lv_style_init(&style_btnm1_bg);
    // lv_style_set_pad_top(&style_btnm1_bg, 5);
    // lv_style_set_pad_bottom(&style_btnm1_bg, 5);
    // lv_style_set_pad_left(&style_btnm1_bg, 15);
    // lv_style_set_pad_right(&style_btnm1_bg, 15);
    // lv_style_set_pad_gap(&style_btnm1_bg, 5);  // 替代 pad_inner

    // static lv_style_t style_btnm1;
    // lv_style_init(&style_btnm1);
    // lv_style_set_radius(&style_btnm1, 5);
    // // lv_style_set_bg_color(&style_btnm1, lv_color_silver());

    // btnm1 = lv_btnmatrix_create(lv_scr_act());
    // lv_obj_set_size(btnm1, 150, 150);
    // lv_obj_center(btnm1);
    // lv_btnmatrix_set_map(btnm1, btnm1_map);
    // // lv_btnmatrix_set_recolor(btnm1, true);
    // lv_obj_add_style(btnm1, &style_btnm1_bg, 0);
    // lv_obj_add_style(btnm1, &style_btnm1, LV_PART_ITEMS);
    // lv_obj_add_event_cb(btnm1, event_handler, LV_EVENT_VALUE_CHANGED, NULL);

    // // 创建 btnm2，演示切换控制属性
    // lv_obj_t * btnm2 = lv_btnmatrix_create(lv_scr_act());
    // lv_obj_set_size(btnm2, 220, 50);
    // lv_obj_align_to(btnm2, btnm1, LV_ALIGN_OUT_BOTTOM_MID, 0, 10);
    // lv_btnmatrix_set_map(btnm2, btnm2_map);
    // lv_btnmatrix_set_btn_width(btnm2, 1, 2);
    // lv_btnmatrix_set_btn_ctrl_all(btnm2, LV_BTNMATRIX_CTRL_CHECKABLE);
    // // lv_btnmatrix_set_btn_ctrl(btnm2, 2, LV_BTNMATRIX_CTRL_CHECK_STATE);
    // lv_obj_add_event_cb(btnm2, event_handler, LV_EVENT_VALUE_CHANGED, NULL);
}

