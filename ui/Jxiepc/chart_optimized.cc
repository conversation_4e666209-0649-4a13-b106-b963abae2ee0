#include "chart.h"
#include "lvgl/src/core/lv_event.h"
#include <iostream>
#include "lvgl/src/extra/widgets/chart/lv_chart.h"
#include <stdio.h>
#include <string.h>
#include <unistd.h>

static lv_obj_t * chart1;
static lv_chart_series_t * ser1;
static lv_chart_series_t * ser2;
static lv_chart_cursor_t * cursor;
static lv_obj_t *label;
static lv_obj_t *slider;  // 滚动条对象，设为全局便于更新
static uint16_t last_id = 0;

// 自动跟踪标志，控制是否自动跟踪最新数据
static bool auto_track = true;
static bool is_change = false;

// 历史数据管理
#define MAX_HISTORY_SIZE 1000                     // 最大历史数据点数量
#define VISIBLE_POINTS 10                         // 可见点数量
static lv_coord_t history_ser1[MAX_HISTORY_SIZE]; // 保存历史数据
static lv_coord_t history_ser2[MAX_HISTORY_SIZE];
static uint32_t history_count = 0;          // 当前历史数据点数量
static uint32_t display_start_idx = 0;      // 当前显示的起始索引
static uint32_t last_display_start_idx = 0; // 上次显示的起始索引
static uint32_t last_history_count = 0;     // 上次数据总数

static uint32_t add_num = 30;

// 函数声明
static void update_chart_display();
static void update_slider_position();


static void draw_event_cb(lv_event_t * e)
{
    lv_obj_t * obj = lv_event_get_target(e);

    /*Add the faded area before the lines are drawn*/
    lv_obj_draw_part_dsc_t * dsc = lv_event_get_draw_part_dsc(e);
    if(dsc->part == LV_PART_ITEMS) {
        if(!dsc->p1 || !dsc->p2) return;

        /*Add a line mask that keeps the area below the line*/
        lv_draw_mask_line_param_t line_mask_param;
        lv_draw_mask_line_points_init(&line_mask_param, dsc->p1->x, dsc->p1->y, dsc->p2->x, dsc->p2->y,
                                      LV_DRAW_MASK_LINE_SIDE_BOTTOM);
        int16_t line_mask_id = lv_draw_mask_add(&line_mask_param, NULL);

        /*Add a fade effect: transparent bottom covering top*/
        lv_coord_t h = lv_obj_get_height(obj);
        lv_draw_mask_fade_param_t fade_mask_param;
        lv_draw_mask_fade_init(&fade_mask_param, &obj->coords, LV_OPA_COVER, obj->coords.y1 + h / 8, LV_OPA_TRANSP,
                               obj->coords.y2);
        int16_t fade_mask_id = lv_draw_mask_add(&fade_mask_param, NULL);

        /*Draw a rectangle that will be affected by the mask*/
        lv_draw_rect_dsc_t draw_rect_dsc;
        lv_draw_rect_dsc_init(&draw_rect_dsc);
        draw_rect_dsc.bg_opa = LV_OPA_20;
        draw_rect_dsc.bg_color = dsc->line_dsc->color;

        lv_area_t a;
        a.x1 = dsc->p1->x;
        a.x2 = dsc->p2->x - 1;
        a.y1 = LV_MIN(dsc->p1->y, dsc->p2->y);
        a.y2 = obj->coords.y2;
        lv_draw_rect(dsc->draw_ctx, &draw_rect_dsc, &a);

        /*Remove the masks*/
        lv_draw_mask_free_param(&line_mask_param);
        lv_draw_mask_free_param(&fade_mask_param);
        lv_draw_mask_remove_id(line_mask_id);
        lv_draw_mask_remove_id(fade_mask_id);
    }
    /*Hook the division lines too*/
    else if(dsc->part == LV_PART_MAIN) {
        if(dsc->line_dsc == NULL || dsc->p1 == NULL || dsc->p2 == NULL) return;

        /*Vertical line*/
        if(dsc->p1->x == dsc->p2->x) {
            dsc->line_dsc->color  = lv_palette_lighten(LV_PALETTE_RED, 1);
            if(dsc->id == 3) {
                dsc->line_dsc->width  = 2;
                dsc->line_dsc->dash_gap  = 0;
                dsc->line_dsc->dash_width  = 0;
            }
            else {
                dsc->line_dsc->width = 1;
                dsc->line_dsc->dash_gap  = 6;
                dsc->line_dsc->dash_width  = 6;
            }
        }
        /*Horizontal line*/
        else {
            if(dsc->id == 2) {
                dsc->line_dsc->width  = 2;
                dsc->line_dsc->dash_gap  = 0;
                dsc->line_dsc->dash_width  = 0;
            }
            else {
                dsc->line_dsc->width = 2;
                dsc->line_dsc->dash_gap  = 6;
                dsc->line_dsc->dash_width  = 6;
            }

            if(dsc->id == 1  || dsc->id == 3) {
                dsc->line_dsc->color  = lv_palette_main(LV_PALETTE_GREEN);
            }
            else {
                dsc->line_dsc->color  = lv_palette_lighten(LV_PALETTE_GREY, 1);
            }
        }
    }
    else if(dsc->part == LV_PART_TICKS && dsc->text) {
        // 自定义X轴标签文本，显示实际的数据点索引
        if(dsc->text) {
            // 检查是否是X轴标签
            if(dsc->id >= 0 && dsc->id < 5) {  // X轴标签的ID通常是0-4
                // 计算当前标签对应的数据点索引
                uint32_t idx = display_start_idx + dsc->value;
                // 修改标签文本为数据点索引
                char buf[16];
                snprintf(buf, sizeof(buf), "%d", idx);
                strcpy(dsc->text, buf);
            }
        }
    }
}

// 添加新数据并存储到历史记录
static void add_data(lv_timer_t * timer)
{
    if(add_num <= 0) {
        return;
    }
    LV_UNUSED(timer);
    static uint32_t cnt = 0;
    
    // 生成新数据
    lv_coord_t val1 = cnt;
    lv_coord_t val2 = cnt % 4 == 0 ? lv_rand(40, 60) : history_ser2[history_count > 0 ? history_count-1 : 0];
    
    // 保存到历史数据
    if(history_count < MAX_HISTORY_SIZE) {
        history_ser1[history_count] = val1;
        history_ser2[history_count] = val2;
        history_count++;
    } else {
        // 历史数据满了，移动数据
        for(uint32_t i = 0; i < MAX_HISTORY_SIZE - 1; i++) {
            history_ser1[i] = history_ser1[i + 1];
            history_ser2[i] = history_ser2[i + 1];
        }
        history_ser1[MAX_HISTORY_SIZE - 1] = val1;
        history_ser2[MAX_HISTORY_SIZE - 1] = val2;
    }
    
    // 如果是自动跟踪模式，调整显示区域到最新数据
    if(auto_track) {
        display_start_idx = history_count > VISIBLE_POINTS ? history_count - VISIBLE_POINTS : 0;
    }
    
    add_num--;
    // 更新图表显示
    update_chart_display();
    
    // 更新滑块位置，与图表数据保持一致
    update_slider_position();
    
    cnt++;
}


static void update_chart_all_display() {
    uint16_t point_count = lv_chart_get_point_count(chart1);
    
    lv_chart_set_all_value(chart1, ser1, 0);
    lv_chart_set_all_value(chart1, ser2, 0);
        
    for(uint16_t i = 0; i < point_count; i++) {
        uint32_t idx = display_start_idx + i;
        if(idx < history_count) {
            lv_chart_set_value_by_id(chart1, ser1, i, history_ser1[idx]);
            lv_chart_set_value_by_id(chart1, ser2, i, history_ser2[idx]);
        }
    }
    
    // 刷新图表
    lv_chart_refresh(chart1);
}

static void update_chart_display_sliding() {
    uint16_t point_count = lv_chart_get_point_count(chart1);
    
    // 计算显示索引差异
    int diff = display_start_idx - last_display_start_idx;
    
    std::cout << "diff: " << diff << " point_count: " << point_count << " last_history_count: " << last_history_count << " history_count: " << history_count << " display_start_idx: " << display_start_idx << " last_display_start_idx: " << last_display_start_idx << std::endl;
    // 无变化或数据总量变化时使用全量更新
    if(diff == 0 || last_history_count != history_count) {
        update_chart_all_display();
    }else {
        // 使用点滑动方法进行增量更新
        if(abs(diff) < point_count) {
            // 滑动方向不同处理
            if(diff > 0) {
                // 向左滑动：移除左侧点，添加右侧新点
                std::cout << "向左滑动" << std::endl;
                for(int i = 0; i < diff; i++) {

                    // 添加右侧新点
                    uint32_t new_idx = display_start_idx + point_count - 1 - i;
                    if(new_idx < history_count) {
                        lv_chart_set_next_value(chart1, ser1, history_ser1[new_idx]);
                        lv_chart_set_next_value(chart1, ser2, history_ser2[new_idx]);
                    } else {
                        lv_chart_set_next_value(chart1, ser1, 0);
                        lv_chart_set_next_value(chart1, ser2, 0);
                    }
                }
            } else {
                // 向右滑动：移除右侧点，添加左侧新点
                diff = -diff; // 转为正数
                std::cout << "向右滑动: " << diff << std::endl;
                for(int i = 0; i < diff; i++) {
                    // 在开头添加新点
                    uint32_t new_idx = display_start_idx + i;
                    if(new_idx < history_count) {
                        lv_chart_set_value_by_id(chart1, ser1, i, history_ser1[new_idx]);
                        lv_chart_set_value_by_id(chart1, ser2, i, history_ser2[new_idx]);
                    } else {
                        lv_chart_set_value_by_id(chart1, ser1, i, 0);
                        lv_chart_set_value_by_id(chart1, ser2, i, 0);
                    }
                }
            }
        } else {
            // 差异太大，使用全量更新
            update_chart_all_display();
        }
    }
    
    // 更新记录的状态
    last_display_start_idx = display_start_idx;
    last_history_count = history_count;
}

// 更新图表显示
static void update_chart_display() {
    update_chart_display_sliding();
    //uint16_t point_count = lv_chart_get_point_count(chart1);
    
    //// 计算可见数据的范围
    //uint16_t display_count = history_count < point_count ? history_count : point_count;
    
    //// 检查是否需要完全刷新
    //bool need_full_refresh = false;
    
    //// 如果显示的起始索引变化了超过1个点，或者数据总量发生变化，需要完全刷新
    //if(last_display_start_idx != display_start_idx || last_history_count != history_count) {
    //    need_full_refresh = true;
    //}
    
    //if(need_full_refresh) {
    //    // 需要完全刷新时清空并重新填充所有点
    //    lv_chart_set_all_value(chart1, ser1, 0);
    //    lv_chart_set_all_value(chart1, ser2, 0);
    //    
    //    for(uint16_t i = 0; i < display_count; i++) {
    //        uint32_t idx = display_start_idx + i;
    //        if(idx < history_count) {
    //            lv_chart_set_value_by_id(chart1, ser1, i, history_ser1[idx]);
    //            lv_chart_set_value_by_id(chart1, ser2, i, history_ser2[idx]);
    //        }
    //    }
    //} else if(auto_track && history_count > 0) {
    //    // 自动跟踪模式下，只需更新最新的点
    //    uint16_t pos = display_count - 1;
    //    lv_chart_set_value_by_id(chart1, ser1, pos, history_ser1[history_count - 1]);
    //    lv_chart_set_value_by_id(chart1, ser2, pos, history_ser2[history_count - 1]);
    //}
    
    //// 刷新图表
    //lv_chart_refresh(chart1);
    
    //// 更新记录的状态
    //last_display_start_idx = display_start_idx;
    //last_history_count = history_count;
    // usleep(100000);
}

// 更新滑块位置以匹配当前显示区域
static void update_slider_position() {

    if(!slider) return;
    
    // 避免数据为0时的除零错误
    if(history_count <= VISIBLE_POINTS) {
        lv_slider_set_value(slider, 100, LV_ANIM_ON);
        return;
    }
    
    // 计算滑块位置百分比 = 当前显示位置 / 最大可能位置 * 100
    uint32_t max_start_idx = history_count - VISIBLE_POINTS;
    int32_t slider_value = (display_start_idx * 100) / max_start_idx;
    
    // 限制在0-100之间
    if(slider_value < 0) slider_value = 0;
    if(slider_value > 100) slider_value = 100;
    
    // 设置滑块位置，带动画效果
    lv_slider_set_value(slider, slider_value, LV_ANIM_ON);
}

// 滚动条事件回调
static void slider_event_cb(lv_event_t * e) {
    is_change = true;
    lv_obj_t * slider = lv_event_get_target(e);
    
    // 用户正在拖动滑块，关闭自动跟踪
    // 定时将其置为true
    auto_track = false;
    
    // 获取滑块值
    int32_t v = lv_slider_get_value(slider);
    
    // 计算最大可能的起始索引
    uint32_t max_start_idx = history_count > VISIBLE_POINTS ? history_count - VISIBLE_POINTS : 0;
    
    // 根据滑块值计算新的显示起始索引
    display_start_idx = (v * max_start_idx) / 100;
    
    // 更新图表显示
    update_chart_display();
}

void chart_optimized() {
    /*创建图表*/
    chart1 = lv_chart_create(lv_scr_act());
    lv_obj_set_size(chart1, 400, 220);
    lv_obj_align(chart1, LV_ALIGN_TOP_MID, 0, 10);
    
    // 设置图表类型和属性
    lv_chart_set_type(chart1, LV_CHART_TYPE_LINE);
    lv_chart_set_point_count(chart1, VISIBLE_POINTS);  // 显示固定数量的点
    
    // 启用网格线和刻度
    lv_chart_set_div_line_count(chart1, 0, 20); // X轴和Y轴的分割线数量
    
    // 添加事件回调
    lv_obj_add_event_cb(chart1, draw_event_cb, LV_EVENT_DRAW_PART_BEGIN, NULL);
    // TODO
    lv_chart_set_update_mode(chart1, LV_CHART_UPDATE_MODE_SHIFT);

    /*添加两个数据序列*/
    ser1 = lv_chart_add_series(chart1, lv_palette_main(LV_PALETTE_RED), LV_CHART_AXIS_PRIMARY_Y);
    ser2 = lv_chart_add_series(chart1, lv_palette_main(LV_PALETTE_BLUE), LV_CHART_AXIS_SECONDARY_Y);

    // 设置Y轴范围
    lv_chart_set_range(chart1, LV_CHART_AXIS_PRIMARY_Y, 0, 100);
    lv_chart_set_range(chart1, LV_CHART_AXIS_PRIMARY_X, 0, VISIBLE_POINTS);
    
    // 设置X轴和Y轴的刻度
    lv_chart_set_axis_tick(chart1, LV_CHART_AXIS_PRIMARY_X, 10, 5, 10, 2, true, 40);
    lv_chart_set_axis_tick(chart1, LV_CHART_AXIS_PRIMARY_Y, 10, 5, 10, 2, true, 50);
    
    /*创建导航控件*/
    // 创建滑块
    slider = lv_slider_create(lv_scr_act());
    lv_obj_set_size(slider, 280, 20);
    lv_obj_align_to(slider, chart1, LV_ALIGN_TOP_MID, 0, 230);
    lv_obj_add_event_cb(slider, slider_event_cb, LV_EVENT_VALUE_CHANGED, NULL);
    
    // 设置滑块初始位置为最右侧
    lv_slider_set_value(slider, 100, LV_ANIM_OFF);
    
    // 添加定时器周期性添加数据
    lv_timer_create(add_data, 500, NULL);
}

