#include "lvgl/lvgl.h"
#include <iostream>
#include "chart.h"
#include "lvgl/lvgl.h"
#include "lvgl/src/core/lv_obj_pos.h"
#include "lvgl/src/extra/widgets/chart/lv_chart.h"
#include "lvgl/src/widgets/lv_dropdown.h"

static lv_obj_t * chart;

static void zoom_x_cb(lv_event_t * e) {
    lv_obj_t * chart = (lv_obj_t *)lv_event_get_user_data(e);
    uint16_t zoom_x = lv_chart_get_zoom_x(chart);
    lv_chart_set_zoom_x(chart, zoom_x + 64);
}

static void zoom_y_cb(lv_event_t * e) {
    lv_obj_t * chart = (lv_obj_t *)lv_event_get_user_data(e);
    uint16_t zoom_y = lv_chart_get_zoom_y(chart);
    lv_chart_set_zoom_y(chart, zoom_y + 64);
}

static void reset_cb(lv_event_t * e) {
    lv_obj_t * chart = (lv_obj_t *)lv_event_get_user_data(e);
    lv_chart_set_zoom_x(chart, 256);
    lv_chart_set_zoom_y(chart, 256);
}

static int16_t zoom_x = 1;
static int16_t zoom_y = 1;
static int max_x = 100;
static int max_y = 100;
static int min_x = 0;
static int min_y = 0;
static int s = 10;
void chart_menu(void) {
    // 创建图表
    chart = lv_chart_create(lv_scr_act());
    lv_obj_set_size(chart, 250, 150);
    lv_obj_align(chart, LV_ALIGN_CENTER, 0, 0);

    lv_chart_set_type(chart, LV_CHART_TYPE_LINE);
    lv_chart_set_point_count(chart, 10);
    lv_chart_set_range(chart, LV_CHART_AXIS_PRIMARY_X, min_x, max_x);
    lv_chart_set_range(chart, LV_CHART_AXIS_PRIMARY_Y, min_y, max_y);

    lv_chart_series_t * ser = lv_chart_add_series(chart, lv_palette_main(LV_PALETTE_BLUE), LV_CHART_AXIS_PRIMARY_Y);
    for(int i = 0; i < 10; i++) {
        ser->y_points[i] = lv_rand(10, 100);
    }
    lv_chart_refresh(chart);

    // 创建一个下拉列表
    lv_obj_t * dropdown = lv_dropdown_create(lv_scr_act());
    lv_dropdown_set_options(dropdown, "bx\n"
                                     "by\n"
                                     "sx\n"
                                     "sy");
    lv_dropdown_set_selected(dropdown, 0);  // 预先选择第一个选项
    lv_dropdown_set_text(dropdown, "");     // 将显示文本设置为空
    
    // 设置下拉列表的大小和位置（右上角）
    lv_obj_set_size(dropdown, 40, 40);
    lv_obj_align_to(dropdown, chart, LV_ALIGN_TOP_RIGHT, 10, -10);
    
    // 设置下拉列表的半透明效果
    lv_obj_set_style_bg_color(dropdown, lv_color_black(), LV_PART_MAIN |LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(dropdown, LV_OPA_50, LV_PART_MAIN |LV_STATE_DEFAULT);
    //lv_obj_set_style_bg_color(dropdown, lv_palette_darken(LV_PALETTE_BLUE, 2), LV_PART_SELECTED | LV_STATE_PRESSED);
    //lv_obj_set_style_bg_color(dropdown, lv_palette_darken(LV_PALETTE_BLUE, 3), LV_PART_SELECTED | LV_STATE_CHECKED);
    // lv_obj_set_style_bg_color(dropdown, lv_color_black(), LV_PART_SELECTED);
    // lv_obj_set_style_bg_opa(dropdown, LV_OPA_50, LV_PART_SELECTED);
    lv_obj_t * list = lv_dropdown_get_list(dropdown);
    lv_obj_set_size(list, 40, 40);

    lv_dropdown_set_selected_highlight(dropdown, false);
    lv_obj_set_style_bg_color(list, lv_color_black(), LV_PART_MAIN |LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(list,LV_OPA_50, LV_PART_MAIN |LV_STATE_DEFAULT);
    
    // 修改下拉列表的箭头符号
    lv_dropdown_set_symbol(dropdown, LV_SYMBOL_SETTINGS);
    
    // 自定义下拉箭头的样式
    lv_obj_set_style_text_font(dropdown, &lv_font_montserrat_14, LV_PART_MAIN);
    lv_obj_set_style_text_color(dropdown, lv_color_white(), LV_PART_MAIN);
    
    lv_obj_add_event_cb(dropdown, [](lv_event_t * e) {
        lv_event_code_t code = lv_event_get_code(e);
        lv_obj_t * obj = lv_event_get_target(e);

        // 拦截 CLICKED，不让它收起
        if(code == LV_EVENT_CLICKED) {
            // lv_dropdown_close()，达到“保持展开”的目的
            // 什么都不做，就不会收起
        }

        // 但仍然处理 VALUE_CHANGED
        else if(code == LV_EVENT_VALUE_CHANGED) {
            uint16_t selected = lv_dropdown_get_selected(obj);
            LV_LOG_USER("选中 %d: %s", selected, txt);
        }

    }, LV_EVENT_ALL, NULL);

    // 设置下拉列表打开时的半透明效果
    // lv_obj_set_style_bg_opa(dropdown, LV_OPA_70, LV_PART_LIST);
    
    // 添加事件回调函数
    lv_obj_add_event_cb(dropdown, [](lv_event_t * e) {
        lv_obj_t * dropdown = lv_event_get_target(e);
        lv_obj_t * chart = (lv_obj_t *)lv_event_get_user_data(e);
        
        if(e->code == LV_EVENT_VALUE_CHANGED) {
            uint16_t selected = lv_dropdown_get_selected(dropdown);
            switch(selected) {
                case 0: // 线条
                    // 扩大
                    // lv_chart_set_type(chart, LV_CHART_TYPE_LINE);
                    zoom_x += 1;
                    min_x += s * zoom_x;
                    max_x -= s * zoom_x;
                    if(min_x > max_x) {
                        return;
                    }
                    printf("b min_x: %d, max_x: %d\n", min_x, max_x);
                    // lv_chart_set_range(chart, LV_CHART_AXIS_PRIMARY_X, min_x, max_x);
                    lv_chart_set_point_count(chart, 5);
                    // lv_chart_set_zoom_x(chart, 256* zoom_x);
                    break;
                case 1: // 柱状图
                    zoom_y += 1;
                    min_y += s * zoom_y;
                    max_y -= s * zoom_y;
                    if(min_y > max_y) {
                        return;
                    }
                    printf("s min_y: %d, max_y: %d\n", min_y, max_y);
                    lv_chart_set_range(chart, LV_CHART_AXIS_PRIMARY_Y, min_y, max_y);
                    // lv_chart_set_zoom_y(chart, 256* zoom_y);
                    break;
                case 2: // 散点图
                    zoom_x -= 1;
                    min_x -= s * zoom_x;
                    max_x += s * zoom_x;
                    printf("s min_x: %d, max_x: %d\n", min_x, max_x);
                    // lv_chart_set_range(chart, LV_CHART_AXIS_PRIMARY_X, min_x, max_x);
                    lv_chart_set_point_count(chart, 10);
                    // lv_chart_set_zoom_x(chart, 256* zoom_x);
                    break;
                case 3: // 面积图
                    zoom_y -= 1;
                    min_y -= s * zoom_y;
                    max_y += s * zoom_y;
                    if(min_y > max_y) {
                        return;
                    }
                    printf("b min_y: %d, max_y: %d\n", min_y, max_y);
                    lv_chart_set_range(chart, LV_CHART_AXIS_PRIMARY_Y, min_y, max_y);
                    // lv_chart_set_zoom_y(chart, 256* zoom_y);
                    // 为面积图添加填充效果
                    // lv_obj_set_style_bg_opa(chart, LV_OPA_50, LV_PART_ITEMS);
                    break;
            }
            lv_dropdown_open(dropdown);
            lv_chart_refresh(chart);
        }else if(e->code == LV_EVENT_CLICKED) {
            lv_dropdown_open(dropdown);
        }
    }, LV_EVENT_ALL, chart);
}
