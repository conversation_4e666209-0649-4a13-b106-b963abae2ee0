#include "chart.h"
#include "lvgl/src/core/lv_event.h"
#include "lvgl/src/extra/widgets/chart/lv_chart.h"
#include <stdio.h>
#include <string.h>
#include <math.h>

static lv_obj_t *chart;
static lv_chart_series_t *ser;

static void chart_scroll_event_cb(lv_event_t *e) {
    printf("chart_scroll_event_cb\n");
    lv_obj_t *obj = lv_event_get_target(e);
    lv_coord_t scroll_x = lv_obj_get_scroll_x(obj);

    // 计算 X 轴范围
    lv_coord_t points_to_show = 100;
    lv_coord_t start_x = scroll_x;
    lv_coord_t end_x = scroll_x + points_to_show - 1;

    // 限制范围
    if (end_x >= 1000) {
        end_x = 999;
        start_x = end_x - points_to_show + 1;
    }
    if (start_x < 0) {
        start_x = 0;
        end_x = points_to_show - 1;
    }

    // 更新图表 X 轴范围
    lv_chart_set_range(chart, LV_CHART_AXIS_PRIMARY_X, start_x, end_x);
    lv_chart_refresh(chart);

    // 滚动结束时触发吸附
    if (lv_event_get_code(e) == LV_EVENT_SCROLL_END) {
        lv_obj_update_snap(obj, LV_ANIM_ON);
    }

    LV_LOG_USER("Scroll X: %d, X Range: %d to %d", scroll_x, start_x, end_x);
}

void chart_draw_slid(void) {
    // 创建图表
    lv_obj_t *chart = lv_chart_create(lv_scr_act());
    lv_obj_set_size(chart, 300, 200);
    lv_obj_align(chart, LV_ALIGN_CENTER, 0, 0);

    // 设置图表类型和数据点
    lv_chart_set_type(chart, LV_CHART_TYPE_LINE);
    //lv_chart_set_point_count(chart, 1000);
    lv_chart_set_range(chart, LV_CHART_AXIS_PRIMARY_X, 0, 99);
    lv_chart_set_range(chart, LV_CHART_AXIS_PRIMARY_Y, 0, 100);

    // 启用滚动和吸附
    lv_obj_set_scroll_dir(chart, LV_DIR_HOR | LV_DIR_RIGHT);
    lv_obj_set_scrollbar_mode(chart, LV_SCROLLBAR_MODE_ON);
    lv_obj_add_flag(chart, LV_OBJ_FLAG_SCROLLABLE | LV_OBJ_FLAG_CLICKABLE);
    lv_obj_set_scroll_snap_x(chart, LV_SCROLL_SNAP_CENTER);

    // 添加数据系列
    ser = lv_chart_add_series(chart, lv_palette_main(LV_PALETTE_RED), LV_CHART_AXIS_PRIMARY_Y);
    for (int i = 0; i < 100; i++) {
        lv_chart_set_next_value(chart, ser, i);
    }

    // 注册事件
    lv_obj_add_event_cb(chart, chart_scroll_event_cb, LV_EVENT_SCROLL, NULL);
    lv_obj_add_event_cb(chart, chart_scroll_event_cb, LV_EVENT_SCROLL_END, NULL);

    // 刷新图表
    lv_chart_refresh(chart);
}
