#include "lvgl/src/misc/lv_color.h"
#include "util.h"
#include <iostream>
static void roller_event_handler(lv_event_t *e) {
    lv_obj_t *roller = lv_event_get_target(e);
    char buf[32];
    lv_roller_get_selected_str(roller, buf, sizeof(buf));
    uintptr_t roller_id = (uintptr_t)lv_event_get_user_data(e); // 获取滚轮标识
    const char *type = roller_id == 0 ? "Hour" : (roller_id == 1 ? "Minute" : "Second");
    LV_LOG_USER("%s selected: %s", type, buf);
}

static void event_handler3(lv_event_t * e)
{
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t * obj = lv_event_get_current_target(e);
    if(code == LV_EVENT_VALUE_CHANGED)
    {
        lv_calendar_date_t date;
        if(lv_calendar_get_pressed_date(obj, &date))
        {
            std::cout<<"111选中日期："<<(int)date.year<<(int)date.month<<(int)date.day<<std::endl;
        }
    }
}
// 
// static void event_draw_cb(lv_event_t * e) {
//     lv_obj_t * calendar = lv_event_get_target(e);
//     lv_calendar_date_t date;
// 
//     /*
//     容器：一个矩形，是标题和天数的容器。仅使用LV_PART_MAIN，其中所有背景相关样式属性都在工作。
//     天数：它是引擎盖下的按钮矩阵对象，用于将天数安排到矩阵中。lv_calendar_get_btnmatrix(calendar)可用于获取它。
//     LV_PART_MAIN日历的背景。使用所有与背景相关的样式属性。
//     LV_PART_ITEMS指日期和日期名称。按钮矩阵控制标志被设置为区分按钮，并添加了自定义抽屉事件，修改按钮的属性如下：
//     日期名称没有边框，没有背景，用灰色绘制
//     上个月和下个月的天数有LV_BTNMATRIX_CTRL_DISABLED标志
//     今天的主题原色边框更厚
//     突出显示的日子与主题的原色有一些不透明度。
//     标题：默认未创建，详细信息取决于给定的标题。
//     */
//     lv_draw_ctx_t * draw_ctx = lv_event_get_draw_ctx(e);
//     lv_draw_rect_dsc_t rect_dsc;
//     lv_draw_rect_dsc_init(&rect_dsc);
// 
//     // 获取日历对象的按钮矩阵
//     lv_obj_t * btnm = lv_calendar_get_btnmatrix(calendar);
//     
//     // 设置日期名称的样式
//     rect_dsc.border_width = 0;
//     rect_dsc.bg_opa = LV_OPA_TRANSP;
//     rect_dsc.bg_color = lv_color_hex(0x888888);
//     
//     // 获取当前日期
//     const lv_calendar_date_t *today = lv_calendar_get_today_date(calendar);
//     
//     // 设置今天日期的特殊样式
//     if(lv_calendar_get_pressed_date(calendar, &date)) {
//         if(date.year == today->year && date.month == today->month && date.day == today->day) {
//             rect_dsc.border_width = 2;
//             rect_dsc.border_color = lv_theme_get_color_primary(calendar);
//         }
//     }
//     
//     // 设置高亮日期的样式
//     lv_calendar_date_t * highlighted = NULL;
//     uint16_t highlighted_cnt = 0;
//     highlighted = lv_calendar_get_highlighted_dates(calendar);
//     
//     for(uint16_t i = 0; i < highlighted_cnt; i++) {
//         if(date.year == highlighted[i].year && date.month == highlighted[i].month && 
//            date.day == highlighted[i].day) {
//             rect_dsc.bg_opa = LV_OPA_40;
//             rect_dsc.bg_color = lv_theme_get_color_primary(calendar);
//             break;
//         }
//     }
//     
//     // 绘制矩形
//     lv_area_t area;
//     area.x1 = calendar->coords.x1;
//     area.y1 = calendar->coords.y1;
//     area.x2 = calendar->coords.x2;
//     area.y2 = calendar->coords.y2;
//     lv_draw_rect(draw_ctx, &rect_dsc, &area);
// }
 
 // 自定义绘制事件处理函数
static void calendar_draw_event_cb(lv_event_t * e)
{
    lv_obj_t * obj = lv_event_get_target(e);
    lv_obj_t * calendar = (lv_obj_t *)lv_event_get_user_data(e);
    lv_obj_draw_part_dsc_t * dsc = lv_event_get_draw_part_dsc(e);

    // std::cout << "type: " << dsc->type << std::endl;
    // std::cout << "part: " << dsc->part << std::endl;
    // std::cout << "id: " << dsc->id << std::endl;
    // std::cout << "value: " << dsc->value << std::endl;
    // std::cout << "--------------------------------" << std::endl;

    if (dsc->part == LV_PART_ITEMS) {
        uint32_t row = dsc->id / 7; // 假设每行7列
        uint32_t col = dsc->id % 7;

        // 获取按钮矩阵
        lv_obj_t * btnm = lv_calendar_get_btnmatrix(calendar);
        const char * txt = lv_btnmatrix_get_btn_text(btnm, dsc->id);

        // 日期名称（第一行）
        if (row == 0) {
            std::cout << "row: " << row << ": text: " << txt << std::endl;
            // dsc->label_dsc->color = lv_color_hex(0x00FF00);
            dsc->rect_dsc->bg_opa = LV_OPA_100; // 无背景
            dsc->rect_dsc->bg_color = lv_color_hex(0x00FF00);
            dsc->rect_dsc->border_width = 20; // 无边框
            dsc->rect_dsc->border_color = lv_color_hex(0x00FF00);
            dsc->label_dsc->color = lv_color_hex(0xFF0000);
            // dsc->label_dsc->sel_bg_color= lv_color_hex(0xFFFF00);
            // dsc->label_dsc->opa = LV_OPA_100;
        }
        // 今天
        else if (lv_calendar_get_today_date(calendar)->day == atoi(txt)) {
            dsc->rect_dsc->border_color = lv_theme_get_color_primary(obj); // 主题原色
            dsc->rect_dsc->border_width = 3; // 更厚的边框
        }
        // 突出显示的日期（示例：假设日期 15 为突出显示）
        else if (atoi(txt) == 5 || strcmp(txt, "Mon") == 0) {
            // dsc->rect_dsc->bg_color = lv_theme_get_color_primary(obj); // 主题原色
            dsc->rect_dsc->bg_color = lv_color_hex(0x00FF00); // 主题原色
            dsc->rect_dsc->bg_opa = LV_OPA_50; // 50% 不透明度
        }
    }
}
 
static void main_calendar_draw_event_cb(lv_event_t * e) {
    lv_obj_t * obj = lv_event_get_target(e);
    lv_obj_t * calendar = (lv_obj_t *)lv_event_get_user_data(e);
    lv_obj_draw_part_dsc_t * dsc = lv_event_get_draw_part_dsc(e);
    
    std::cout << "id: " << dsc->id << std::endl;
    std::cout << "part: " << dsc->part << std::endl;
    std::cout << "type: " << dsc->type << std::endl;
    std::cout << "value: " << dsc->value << std::endl;
    std::cout << "--------------------------------" << std::endl;
    if(dsc->type == 1) {
        // dsc->rect_dsc->bg_color = lv_color_hex(0x00FF00);
        // dsc->rect_dsc->bg_opa = LV_OPA_50;
        // dsc->label_dsc->color = lv_color_hex(0xFF0000);
        // dsc->label_dsc->opa = LV_OPA_100;
    }else if(dsc->type == 2) {
        //dsc->rect_dsc->bg_color = lv_color_hex(0x00FFFF);
        // dsc->rect_dsc->bg_opa = LV_OPA_50;

        // dsc->label_dsc->color = lv_color_hex(0xFF00FF);
        // dsc->label_dsc->opa = LV_OPA_100;

    }
}

void test_calendar() {
 
    static const char *day_names[] =
    {
        "Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"
    };
 
    lv_obj_t  * calendar = lv_calendar_create(lv_scr_act());
    lv_obj_set_size(calendar, 350, 300);
    lv_obj_set_pos(calendar, 100, 0);
    // lv_obj_set_size(calendar, 250, 200);
    lv_obj_add_event_cb(calendar, event_handler3, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(calendar, main_calendar_draw_event_cb, LV_EVENT_DRAW_PART_BEGIN, calendar);

 
    /// 设置日期名称
    lv_calendar_set_day_names(calendar, day_names);
    /// 设置当前日期
    lv_calendar_set_today_date(calendar, 2030, 05, 23);

    /// 设置显示年月
    lv_calendar_set_showed_date(calendar, 2030, 05);

    /*Highlight a few days*/
    lv_calendar_date_t highlighted_days[3];       /*Only its pointer will be saved so should be static*/
    highlighted_days[0].year = 2025;
    highlighted_days[0].month = 05;
    highlighted_days[0].day = 26;

    highlighted_days[1].year = 2025;
    highlighted_days[1].month = 05;
    highlighted_days[1].day = 27;

    highlighted_days[2].year = 2025;
    highlighted_days[2].month = 05;
    highlighted_days[2].day = 28;

    lv_calendar_set_highlighted_dates(calendar, highlighted_days, 3);

    // 设置显示模式
#if LV_USE_CALENDAR_HEADER_DROPDOWN
    // lv_calendar_header_dropdown_create(calendar);
    lv_calendar_header_arrow_create(calendar);
#elif LV_USE_CALENDAR_HEADER_ARROW
    lv_calendar_header_arrow_create(calendar);
#endif

    // set style
    // 设置日历容器背景样式 (LV_PART_MAIN)
    lv_obj_set_style_bg_color(calendar, lv_color_hex(0xF0F0F0), LV_PART_MAIN);
    lv_obj_set_style_bg_opa(calendar, LV_OPA_COVER, LV_PART_MAIN);
    lv_obj_set_style_border_width(calendar, 1, LV_PART_MAIN);
    lv_obj_set_style_border_color(calendar, lv_color_hex(0x000000), LV_PART_MAIN);
    lv_obj_set_style_pad_all(calendar, 5, LV_PART_MAIN);

    // 按钮矩阵
    lv_obj_t * btnm = lv_calendar_get_btnmatrix(calendar);
    lv_obj_set_style_bg_color(btnm, lv_palette_main(LV_PALETTE_BLUE), LV_PART_ITEMS); 
    lv_obj_add_event_cb(btnm, calendar_draw_event_cb, LV_EVENT_DRAW_PART_BEGIN, calendar);

    // for (uint32_t i = 0; i < 30; i++) {
    //     const char * txt = lv_btnmatrix_get_btn_text(btnm, i);
    //     // 假设前7个和后7个为上个月/下个月（实际需根据日历数据判断）
    //     if (i >= 7 && i < 14) { // 示例范围
    //         lv_btnmatrix_set_btn_ctrl(btnm, i, LV_BTNMATRIX_CTRL_DISABLED);
    //     }
    // }

    // 设置按钮矩阵样式
    // lv_obj_set_style_bg_opa(btnm, LV_OPA_TRANSP, LV_PART_ITEMS);
    // lv_obj_set_style_border_width(btnm, 1, LV_PART_ITEMS);
    // lv_obj_set_style_border_color(btnm, lv_palette_main(LV_PALETTE_GREEN), LV_PART_ITEMS);
    // lv_obj_set_style_text_color(btnm, lv_palette_main(LV_PALETTE_RED), LV_PART_ITEMS);


 
    // 创建小时滚轮
    // lv_obj_t *hour_roller = lv_roller_create(lv_scr_act());
    // lv_obj_set_pos(hour_roller, 100, 300);
    // lv_roller_set_options(hour_roller,
    //                      "00\n01\n02\n03\n04\n05\n06\n07\n08\n09\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n22\n23",
    //                      LV_ROLLER_MODE_INFINITE);
    // lv_roller_set_visible_row_count(hour_roller, 3); // 显示 3 行
    // lv_obj_set_width(hour_roller, 60);
    // lv_obj_add_event_cb(hour_roller, roller_event_handler, LV_EVENT_VALUE_CHANGED, (void *)0);

    // // 创建分钟滚轮
    // lv_obj_t *minute_roller = lv_roller_create(lv_scr_act());
    // lv_roller_set_options(minute_roller,
    //                      "00\n01\n02\n03\n04\n05\n06\n07\n08\n09\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n"
    //                      "21\n22\n23\n24\n25\n26\n27\n28\n29\n30\n31\n32\n33\n34\n35\n36\n37\n38\n39\n40\n41\n42\n"
    //                      "43\n44\n45\n46\n47\n48\n49\n50\n51\n52\n53\n54\n55\n56\n57\n58\n59",
    //                      LV_ROLLER_MODE_INFINITE);
    // lv_roller_set_visible_row_count(minute_roller, 3);
    // lv_obj_set_width(minute_roller, 60);
    // lv_obj_align(minute_roller, LV_ALIGN_CENTER, 0, 0);
    // lv_obj_add_event_cb(minute_roller, roller_event_handler, LV_EVENT_VALUE_CHANGED, (void *)1);

    // // 创建秒滚轮
    // lv_obj_t *second_roller = lv_roller_create(lv_scr_act());
    // lv_obj_set_pos(second_roller, 100, 300);
    // lv_roller_set_options(second_roller,
    //                      "00\n01\n02\n03\n04\n05\n06\n07\n08\n09\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n"
    //                      "21\n22\n23\n24\n25\n26\n27\n28\n29\n30\n31\n32\n33\n34\n35\n36\n37\n38\n39\n40\n41\n42\n"
    //                      "43\n44\n45\n46\n47\n48\n49\n50\n51\n52\n53\n54\n55\n56\n57\n58\n59",
    //                      LV_ROLLER_MODE_INFINITE);
    // lv_roller_set_visible_row_count(second_roller, 3);
    // lv_obj_set_width(second_roller, 60);
    // lv_obj_add_event_cb(second_roller, roller_event_handler, LV_EVENT_VALUE_CHANGED, (void *)2);

    // // 添加分隔符标签 ":"
    // lv_obj_t *colon1 = lv_label_create(lv_scr_act());
    // lv_label_set_text(colon1, ":");
    // lv_obj_align_to(colon1, hour_roller, LV_ALIGN_OUT_RIGHT_MID, 5, 0);

    // lv_obj_t *colon2 = lv_label_create(lv_scr_act());
    // lv_label_set_text(colon2, ":");
    // lv_obj_align_to(colon2, minute_roller, LV_ALIGN_OUT_RIGHT_MID, 5, 0);

    // // 添加单位标签（可选）
    // lv_obj_t *hour_label = lv_label_create(lv_scr_act());
    // lv_label_set_text(hour_label, "h");
    // lv_obj_align_to(hour_label, hour_roller, LV_ALIGN_OUT_BOTTOM_MID, 0, 5);

    // lv_obj_t *minute_label = lv_label_create(lv_scr_act());
    // lv_label_set_text(minute_label, "m");
    // lv_obj_align_to(minute_label, minute_roller, LV_ALIGN_OUT_BOTTOM_MID, 0, 5);

    // lv_obj_t *second_label = lv_label_create(lv_scr_act());
    // lv_label_set_text(second_label, "s");
    // lv_obj_align_to(second_label, second_roller, LV_ALIGN_OUT_BOTTOM_MID, 0, 5);
}
