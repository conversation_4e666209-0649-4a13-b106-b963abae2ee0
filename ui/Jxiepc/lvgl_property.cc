#include <iostream>
#include "lvgl_property.h"


static void btn_event_cb(lv_event_t *e)
{
    std::cout << "========" << std::endl;
}

void test_view() {

    lv_obj_t * btn = lv_btn_create(lv_scr_act());
    
    lv_obj_set_pos(btn, 0, 20);
    lv_obj_set_size(btn, 100, 100);
    //
    // 隐藏按钮
    // lv_obj_add_flag(btn, LV_OBJ_FLAG_HIDDEN);
    
    // 过一会显示出来
    //lv_timer_t * timer = lv_timer_create_basic();
    //lv_timer_set_period(timer, 2000);  // 2秒
    //lv_timer_set_cb(timer, (lv_timer_cb_t) (void *) (intptr_t) ({
    //    lv_obj_clear_flag(btn, LV_OBJ_FLAG_HIDDEN);
    //}));
    
    lv_obj_add_event_cb(btn, btn_event_cb, LV_EVENT_CLICKED, NULL);
    // 再置灰
//     lv_obj_add_state(btn, LV_STATE_DISABLED);
    // lv_obj_clear_flag(btn, LV_EVENT_ALL);
    lv_obj_clear_flag(btn, LV_OBJ_FLAG_CLICKABLE);
    
}


