#include "chart.h"
#include <stdio.h>

static void chart_draw_part_event_cb(lv_event_t *e) {
    lv_obj_t *chart = lv_event_get_target(e);
    lv_obj_draw_part_dsc_t *dsc = lv_event_get_draw_part_dsc(e);

    /* 只处理轴的绘制部分 */
    if (dsc->part == LV_PART_TICKS && dsc->id == LV_CHART_AXIS_PRIMARY_X) {
        /* 获取刻度线的坐标 */
        lv_coord_t x = dsc->p1->x;
        lv_coord_t y_start = lv_obj_get_y(chart);
        lv_coord_t y_end = y_start + lv_obj_get_height(chart);

        /* 绘制垂直网格线，与 X 轴刻度对齐 */
        lv_draw_line_dsc_t line_dsc;
        lv_draw_line_dsc_init(&line_dsc);
        line_dsc.color = lv_color_hex(0x888888);
        line_dsc.width = 1;

        lv_point_t p1 = {x, y_start};
        lv_point_t p2 = {x, y_end};
        printf("p1: %d, %d\n", p1.x, p1.y);
        printf("p2: %d, %d\n", p2.x, p2.y);
        lv_draw_line(dsc->draw_ctx, &line_dsc, &p1, &p2);
    } else if (dsc->part == LV_PART_TICKS && dsc->id == LV_CHART_AXIS_PRIMARY_Y) {
        /* 绘制水平网格线，与 Y 轴刻度对齐 */
        lv_coord_t y = dsc->p1->y;
        lv_coord_t x_start = lv_obj_get_x(chart);
        lv_coord_t x_end = x_start + lv_obj_get_width(chart);

        lv_draw_line_dsc_t line_dsc;
        lv_draw_line_dsc_init(&line_dsc);
        line_dsc.color = lv_color_hex(0x888888);
        line_dsc.width = 1;

        lv_point_t p1 = {x_start, y};
        lv_point_t p2 = {x_end, y};
        lv_draw_line(dsc->draw_ctx, &line_dsc, &p1, &p2);
    }
}

void chart_tick() {

    lv_obj_t *chart1 = lv_chart_create(lv_scr_act());
    lv_obj_set_size(chart1, 600, 400);
    lv_obj_center(chart1);
    lv_obj_set_style_pad_all(chart1, 20, 0); // 增加内边距，为坐标轴刻度留出空间
    //set_style(chart1);
    
    // 设置图表类型和属性
    lv_chart_set_type(chart1, LV_CHART_TYPE_LINE);
    // lv_chart_set_point_count(chart1, 10);
    
    // 启用网格线和刻度
    lv_chart_set_div_line_count(chart1, 21, 19); // X轴和Y轴的分割线数量
    /* 创建虚线样式 */

    // 设置X轴和Y轴的显示
    // lv_obj_add_flag(chart1, LV_OBJ_FLAG_CLICKABLE);
    
    lv_chart_series_t *ser1 = lv_chart_add_series(chart1, lv_palette_main(LV_PALETTE_RED), LV_CHART_AXIS_PRIMARY_Y);
    lv_chart_series_t *ser2 = lv_chart_add_series(chart1, lv_palette_main(LV_PALETTE_BLUE), LV_CHART_AXIS_SECONDARY_Y);
    // 添加事件回调
    lv_chart_set_update_mode(chart1, LV_CHART_UPDATE_MODE_SHIFT);

    // lv_chart_set_zoom_x(chart1, 257);

    // 设置Y轴范围
    lv_chart_set_range(chart1, LV_CHART_AXIS_PRIMARY_Y, 0, 100);
    lv_chart_set_range(chart1, LV_CHART_AXIS_PRIMARY_X, 0, 100);
    
    // 关键部分：设置X轴和Y轴的刻度
    // 参数：图表对象，轴，主刻度线宽，次刻度线宽，主刻度数量，次刻度数量，显示标签，文本长度
    lv_chart_set_axis_tick(chart1, LV_CHART_AXIS_PRIMARY_X, 20, 5, 10, 2, true, 10);
    lv_chart_set_axis_tick(chart1, LV_CHART_AXIS_PRIMARY_Y, 20, 5, 5, 5, false, 50);
    lv_obj_add_flag(chart1, LV_OBJ_FLAG_SCROLL_CHAIN_HOR);

    
    // 填充初始数据
    uint32_t i;
    for(i = 0; i < 20; i++) {
        lv_chart_set_next_value(chart1, ser1, lv_rand(20, 90));
        lv_chart_set_next_value(chart1, ser2, lv_rand(30, 70));
    }

}