#include "base.h"

// 标签指针
static lv_obj_t *label;

// 按钮事件
static void btn_event_cb(lv_event_t *e)
{
    lv_label_set_text(label, "Hello Zephyr Z9!");
}

void lv_example_simple_gui(void)
{
    lv_obj_t *scr = lv_scr_act();
    lv_obj_t * obj1 = lv_obj_create(scr);
    lv_obj_t * obj2 = lv_obj_create(scr);
    lv_obj_set_pos(obj1, 0, 20);
    lv_obj_set_pos(obj1, 300, 20);

    label = lv_label_create(obj1);
    lv_label_set_text(label, "Hello LVGL!");
    lv_obj_align(label, LV_ALIGN_CENTER, 0, -40);

    lv_obj_t *btn = lv_btn_create(obj2);
    lv_obj_align(btn, LV_ALIGN_CENTER, 0, 40);
    lv_obj_add_event_cb(btn, btn_event_cb, LV_EVENT_CLICKED, NULL);

    // lv_obj_t * keyboard = lv_keyboard_create(lv_scr_act());  // 获取或创建键盘对象
    // send_key_event(keyboard, 1);  // 假设 LV_KEYBOARD_BTN_1 是某个键


    //lv_obj_t *btn_label = lv_label_create(btn);
    //lv_label_set_text(btn_label, "Click me");
    //lv_obj_center(btn_label);
}
