#include "chart.h"
#include <cstdlib>
#include <ctime>

// 定义数据点数量
#define POINT_COUNT 10

// 全局变量
static lv_obj_t *chart;
static lv_chart_series_t *ser;
static lv_coord_t y_data[POINT_COUNT];
static int update_count = 0;

// 定时器回调函数，每10秒更新数据
static void update_chart(lv_timer_t *timer)
{
    // 模拟生成新的 Y 数据（这里使用随机数，范围 0-100）
    for (int i = 0; i < POINT_COUNT - 1; i++) {
        y_data[i] = y_data[i + 1]; // 数据左移
    }
    y_data[POINT_COUNT - 1] = rand() % 101; // 新数据点

    // 更新图表数据
    for (int i = 0; i < POINT_COUNT; i++) {
        lv_chart_set_value_by_id(chart, ser, i, y_data[i]);
    }

    // 刷新图表
    lv_chart_refresh(chart);

    // 更新计数（可选，用于调试）
    update_count++;
}

// 初始化并创建 XY 曲线
void test_xy()
{
    // 初始化随机数种子
    srand(time(NULL));

    // 创建 chart 对象
    chart = lv_chart_create(lv_scr_act());
    lv_obj_set_size(chart, 300, 200);
    lv_obj_center(chart);

    // 设置 chart 类型为折线图
    lv_chart_set_type(chart, LV_CHART_TYPE_LINE);

    // 设置数据点数量
    lv_chart_set_point_count(chart, POINT_COUNT);

    // 设置 Y 轴范围
    lv_chart_set_range(chart, LV_CHART_AXIS_PRIMARY_Y, 0, 100);

    // 设置 X 轴刻度
    // lv_chart_set_axis_tick(chart, LV_CHART_AXIS_PRIMARY_X, 10, 5, POINT_COUNT, true, 50);
    // lv_chart_set_axis_tick(chart, LV_CHART_AXIS_PRIMARY_Y, 10, 5, 6, true, 30);

    // 添加数据系列
    ser = lv_chart_add_series(chart, lv_color_hex(0xFF0000), LV_CHART_AXIS_PRIMARY_Y);

    // 初始化数据
    for (int i = 0; i < POINT_COUNT; i++) {
        y_data[i] = rand() % 101; // 随机初始值
        lv_chart_set_value_by_id(chart, ser, i, y_data[i]);
    }

    // 创建定时器，每 10 秒更新一次
    lv_timer_create(update_chart, 10000, NULL);

    // 设置图表样式
    lv_obj_set_style_border_width(chart, 2, 0);
    lv_obj_set_style_border_color(chart, lv_color_hex(0x333333), 0);
    lv_obj_set_style_bg_color(chart, lv_color_hex(0xFFFFFF), 0);
}
