#include "event.h"



void timer_cb(lv_timer_t * timer)
{
    // lv_obj_t * win = (lv_obj_t *)lv_timer_get_user_data(timer); // 获取user_data
    lv_obj_t * win = (lv_obj_t *)timer->user_data; // 获取user_data
    if(win) {
        lv_obj_del(win); // 删除窗口
    }
    lv_timer_del(timer); // 删除自己
}

// 在创建窗口时，挂定时器
void lv_event_win() 
{
    lv_obj_t * win = lv_win_create(lv_scr_act(), 40);
    lv_obj_center(win);

    lv_obj_t * btn = lv_btn_create(lv_scr_act());
    lv_obj_set_size(btn, 200, 200);
    lv_obj_set_size(win, 200, 200);
    lv_obj_set_pos(win, 0, 0);
    lv_obj_set_style_bg_color(
        win,
        lv_color_black(),
        0);

    int timeout_ms = 2000;
    lv_timer_t * timer = lv_timer_create(timer_cb, timeout_ms, win); // timeout_ms毫秒后触发
}

void lv_event_win_page()
{
    lv_obj_t * show_page = lv_obj_create(lv_scr_act()); 
    lv_obj_set_size(show_page, 800, 600);
    
    // 创建三个页面
    lv_obj_t * page1 = lv_obj_create(show_page); 
    lv_obj_set_size(page1, 800, 600);
    
    lv_obj_t * page2 = lv_obj_create(show_page); 
    lv_obj_set_size(page2, 800, 600);
    
    lv_obj_t * page3 = lv_obj_create(show_page); 
    lv_obj_set_size(page3, 800, 600);
    
    // 设置页面的背景为透明，这样可以看到后面的内容
    lv_obj_set_style_bg_opa(page1, LV_OPA_TRANSP, 0);
    lv_obj_set_style_bg_opa(page2, LV_OPA_TRANSP, 0);
    lv_obj_set_style_bg_opa(page3, LV_OPA_TRANSP, 0);
    
    // 关键：设置页面不可点击，让事件穿透
    lv_obj_clear_flag(page1, LV_OBJ_FLAG_CLICKABLE);
    lv_obj_clear_flag(page2, LV_OBJ_FLAG_CLICKABLE);
    lv_obj_clear_flag(page3, LV_OBJ_FLAG_CLICKABLE);
    
    // 创建按钮
    lv_obj_t * btn1 = lv_btn_create(page1);
    lv_obj_t * btn2 = lv_btn_create(page2);
    lv_obj_t * btn3 = lv_btn_create(page3);
    
    // 设置按钮位置
    lv_obj_set_pos(btn1, 50, 50);
    lv_obj_set_pos(btn2, 200, 50);
    lv_obj_set_pos(btn3, 350, 50);
    
    // 设置按钮文本
    lv_obj_t * label1 = lv_label_create(btn1);
    lv_label_set_text(label1, "按钮1");
    lv_obj_center(label1);
    
    lv_obj_t * label2 = lv_label_create(btn2);
    lv_label_set_text(label2, "按钮2");
    lv_obj_center(label2);
    
    lv_obj_t * label3 = lv_label_create(btn3);
    lv_label_set_text(label3, "按钮3");
    lv_obj_center(label3);
    
    // 为按钮添加事件回调
    lv_obj_add_event_cb(btn1, [](lv_event_t * e) {
        LV_LOG("Button 1 clicked");
    }, LV_EVENT_CLICKED, NULL);
    
    lv_obj_add_event_cb(btn2, [](lv_event_t * e) {
        LV_LOG("Button 2 clicked");
    }, LV_EVENT_CLICKED, NULL);
    
    lv_obj_add_event_cb(btn3, [](lv_event_t * e) {
        LV_LOG("Button 3 clicked");
    }, LV_EVENT_CLICKED, NULL);
    
    // 确保所有按钮都是可点击的
    lv_obj_add_flag(btn1, LV_OBJ_FLAG_CLICKABLE);
    lv_obj_add_flag(btn2, LV_OBJ_FLAG_CLICKABLE);
    lv_obj_add_flag(btn3, LV_OBJ_FLAG_CLICKABLE);
    
    // 将按钮移到前景
    lv_obj_move_foreground(btn1);
    lv_obj_move_foreground(btn2);
    lv_obj_move_foreground(btn3);
}

