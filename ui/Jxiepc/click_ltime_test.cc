#include "event_test.h"


static uint32_t press_time = 0;
#define MIN_PRESS_TIME_MS 100

static void my_event_cb(lv_event_t * e) {
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);

    if(code == LV_EVENT_PRESSED) {
        press_time = lv_tick_get();
    } else if(code == LV_EVENT_RELEASED) {
        uint32_t release_time = lv_tick_get();
        if(release_time - press_time >= MIN_PRESS_TIME_MS) {
            // 模拟点击行为
            printf("Long enough press, treating as click\n");
            // 可在这里调用自定义回调或手动触发点击逻辑
        } else {
            printf("Press too short, ignoring\n");
        }
    }
}


void test_click_long_time() {
    //lv_obj_t* label = lv_label_create(lv_scr_act());
    //lv_obj_set_pos(label, 100, 50);
    //
    //lv_obj_add_event_cb(label, my_event_cb, LV_EVENT_ALL, NULL);
    //lv_obj_set_size(label, 225, 35);
    lv_obj_t * label;

    lv_obj_t * btn1 = lv_btn_create(lv_scr_act());
    lv_obj_add_event_cb(btn1, my_event_cb, LV_EVENT_ALL, NULL);
    lv_obj_align(btn1, LV_ALIGN_CENTER, 0, -40);
    // lv_obj_remove_flag(btn1, LV_OBJ_FLAG_PRESS_LOCK);

    label = lv_label_create(btn1);
    lv_label_set_text(label, "Button");
    lv_obj_center(label);

    lv_obj_t * btn2 = lv_btn_create(lv_scr_act());
    lv_obj_add_event_cb(btn2, my_event_cb, LV_EVENT_ALL, NULL);
    lv_obj_align(btn2, LV_ALIGN_CENTER, 0, 40);
    lv_obj_add_flag(btn2, LV_OBJ_FLAG_CHECKABLE);
    lv_obj_set_height(btn2, LV_SIZE_CONTENT);

    label = lv_label_create(btn2);
    lv_label_set_text(label, "Toggle");
    lv_obj_center(label);
}
