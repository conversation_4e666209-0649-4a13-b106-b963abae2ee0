#include "data_transform.h"

// 转换函数
double to_double(void* data, data_all_type_t type) {
    switch (type) {
        case DATA_ALL_TYPE_BOOL:
            return (*(bool*)data) ? 1.0 : 0.0;
        case DATA_ALL_TYPE_UINT8:
            return (double)(*(uint8_t*)data);
        case DATA_ALL_TYPE_INT8:
            return (double)(*(int8_t*)data);
        case DATA_ALL_TYPE_UINT16:
            return (double)(*(uint16_t*)data);
        case DATA_ALL_TYPE_INT16:
            return (double)(*(int16_t*)data);
        case DATA_ALL_TYPE_UINT32:
            return (double)(*(uint32_t*)data);
        case DATA_ALL_TYPE_INT32:
            return (double)(*(int32_t*)data);
        case DATA_ALL_TYPE_FLOAT:
            return (double)(*(float*)data);
        case DATA_ALL_TYPE_DOUBLE:
            return *(double*)data;
        case DATA_ALL_TYPE_STRING:
            return atof((char*)data);
        default:
            return 0.0;
    }
}
