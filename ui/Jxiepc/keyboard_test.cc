#include "keyboard.h"

static const char *my_kb_map[] = {
    "1", "2", "3", "\n",
    "4", "5", "6", "\n",
    "7", "8", "9", "\n",
    "Del", "0", "Enter", ""
};

struct user_data{
    lv_obj_t *label = nullptr;
    lv_obj_t *show = nullptr;
};

static void kb_event_cb(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t *btnm = lv_event_get_target(e);
    const char *txt = lv_btnmatrix_get_btn_text(btnm, lv_btnmatrix_get_selected_btn(btnm));
    user_data *data = (user_data*)lv_event_get_user_data(e);

    if(code == LV_EVENT_VALUE_CHANGED) {
        if(strcmp(txt, "Del") == 0) {
            lv_textarea_del_char(data->show);
        }else if(strcmp(txt, "Enter") == 0) {
            lv_label_set_text(data->label, lv_textarea_get_text(data->show)); 
            lv_obj_t *popup = lv_obj_get_parent(btnm); 
            lv_obj_del(popup);
        }else {
            std::cout << "=======" << txt << std::endl;
            lv_textarea_add_text(data->show, txt);
        }
    }

}


// label 点击事件
static void task_label_event_cb(lv_event_t *e) {
    lv_obj_t *label = lv_event_get_target(e);
    const char *txt = lv_label_get_text(label);
    user_data *data = (user_data*)lv_event_get_user_data(e);
    // 创建一个模态窗口（背景可选透明）
    lv_obj_t *popup = lv_obj_create(lv_scr_act());
    lv_obj_set_size(popup, 300, 200);
    lv_obj_center(popup);
    lv_obj_add_flag(popup, LV_OBJ_FLAG_CLICKABLE);
    lv_obj_set_style_bg_opa(popup, LV_OPA_90, 0);

    // 输入框
    lv_obj_t *ta = lv_textarea_create(popup);
    lv_textarea_add_text(ta, txt);
    lv_obj_set_size(ta, 200, 30);
    lv_obj_align(ta, LV_ALIGN_TOP_MID, 0, 0);

    data->show = ta;
    // 键盘
    lv_obj_t *kb = lv_btnmatrix_create(popup);
    lv_btnmatrix_set_map(kb, my_kb_map);
    lv_obj_set_size(kb, 200, 100);
    lv_obj_align(kb, LV_ALIGN_BOTTOM_MID, 0 ,0);
    lv_obj_add_event_cb(kb, kb_event_cb, LV_EVENT_VALUE_CHANGED, (void*)data);
}

// 主函数
void lv_ex_btnmatrix_test() {
    user_data *data = new user_data; 
    // 创建一些任务 label 模拟点击项
    lv_obj_t *label = lv_label_create(lv_scr_act());
    lv_obj_set_size(label, 200, 100);
    lv_obj_set_style_border_width(label, 2, LV_PART_MAIN);

    lv_obj_align(label, LV_ALIGN_TOP_MID, 0, 0);
    lv_label_set_text(label, "");

    data->label = label;
    lv_obj_add_flag(label, LV_OBJ_FLAG_CLICKABLE); // 可点击
    lv_obj_add_event_cb(label, task_label_event_cb, LV_EVENT_CLICKED, (void*)data);
}
