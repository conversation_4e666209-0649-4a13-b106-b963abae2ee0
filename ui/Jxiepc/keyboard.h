#include "lvgl/lvgl.h"
#include <iostream>

#ifndef UI_JXIEPC_KEYBOARD_H
#define UI_JXIEPC_KEYBOARD_H


void lv_ex_btnmatrix_1(void);
void lv_ex_btnmatrix_2(void);
void lv_ex_btnmatrix_3(lv_indev_t* indev);
void lv_ex_btnmatrix_draw();
void lv_ex_btnmatrix_custom();
void lv_ex_btnmatrix_lv();
void lv_ex_btnmatrix_test();
void lv_ex_btnmatrix_number();
void lv_ex_btnmatrix_string();
void lv_ex_btnmatrix_style();
void lv_ex_btnmatrix_data();


#endif

