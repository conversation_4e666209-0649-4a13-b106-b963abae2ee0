#include <math.h>
#include "lvgl/lvgl.h"

#define POINT_COUNT 100 // 椭圆点的数量

// 计算椭圆边界点
static void calculate_ellipse_points(lv_point_t *points, int num_points, lv_coord_t x0, lv_coord_t y0, lv_coord_t a, lv_coord_t b, float theta) {
    for (int i = 0; i < num_points; i++) {
        float t = (float)i / (num_points - 1) * 2.0f * M_PI;
        float cos_t = cosf(t);
        float sin_t = sinf(t);
        float cos_theta = cosf(theta);
        float sin_theta = sinf(theta);

        points[i].x = x0 + (lv_coord_t)(a * cos_t * cos_theta - b * sin_t * sin_theta);
        points[i].y = y0 + (lv_coord_t)(a * cos_t * sin_theta + b * sin_t * cos_theta);
    }
}

// 绘制椭圆
void draw_ellipse_with_line(void) {
    // 椭圆参数
    lv_coord_t x0 = 200; // 中心x坐标
    lv_coord_t y0 = 200; // 中心y坐标
    lv_coord_t a = 100;  // 长轴半径
    lv_coord_t b = 50;   // 短轴半径
    float theta = M_PI / 4; // 旋转45度

    // 创建line对象
    lv_obj_t *line = lv_line_create(lv_scr_act());
    lv_obj_set_style_line_width(line, 3, 0);
    lv_obj_set_style_line_color(line, lv_color_hex(0xFF0000), 0); // 红色线条
    lv_obj_center(line);

    // 计算椭圆点
    static lv_point_t points[POINT_COUNT];
    calculate_ellipse_points(points, POINT_COUNT, x0, y0, a, b, theta);

    // 设置line控件的点
    lv_line_set_points(line, points, POINT_COUNT);
}

// 生成椭圆半弧线点
static void generate_half_ellipse_arc_points(lv_point_t *points, int num_points, lv_coord_t x0, lv_coord_t y0, lv_coord_t a, lv_coord_t b, float theta_start, float theta_end, float theta_rot) {
    for (int i = 0; i < num_points-1; i++) {
        float t = theta_start + (theta_end - theta_start) * (float)i / (num_points - 1);
        float cos_t = cosf(t);
        float sin_t = sinf(t);
        float cos_theta_rot = cosf(theta_rot);
        float sin_theta_rot = sinf(theta_rot);

        points[i].x = x0 + (lv_coord_t)(a * cos_t * cos_theta_rot - b * sin_t * sin_theta_rot);
        points[i].y = y0 + (lv_coord_t)(a * cos_t * sin_theta_rot + b * sin_t * cos_theta_rot);
    }
    points[num_points-1].x = points[0].x;
    points[num_points-1].y = points[0].y;
}

// 绘制椭圆半弧线
void draw_half_ellipse_arc_with_line(void) {
    // 椭圆半弧线参数
    lv_coord_t x0 = 400; // 中心x坐标（调整以适应大半径）
    lv_coord_t y0 = 400; // 中心y坐标
    lv_coord_t a = 100;  // 长轴半径（水平方向最大半径）
    lv_coord_t b = 300;  // 短轴半径（垂直方向较小半径）
    float theta_start = 0.0f; // 起始角度（0°）
    float theta_end = M_PI/2;  // 终止角度（180°）
    float theta_rot = M_PI ; // 旋转45°

    // 创建line对象
    lv_obj_t *line = lv_line_create(lv_scr_act());
    lv_obj_set_style_line_width(line, 3, 0);
    lv_obj_set_style_line_color(line, lv_color_hex(0x0000FF), 0); // 蓝色线条
    lv_obj_center(line);

    // 生成椭圆半弧线点
    static lv_point_t points[POINT_COUNT];
    generate_half_ellipse_arc_points(points, POINT_COUNT, x0, y0, a, b, theta_start, theta_end, theta_rot);
    lv_line_set_points(line, points, POINT_COUNT);
}


// 生成椭圆扇环点（外弧 + 直线 + 内弧 + 直线）
static void generate_annular_fan_ring_points(lv_point_t *points, int num_points, lv_coord_t x0, lv_coord_t y0, 
                                            lv_coord_t a_outer, lv_coord_t b_outer, lv_coord_t a_inner, lv_coord_t b_inner, 
                                            float theta_start, float theta_end, float theta_rot) {
    int idx = 0;
    int cell_num = num_points/2;
    // 外弧点（从 theta_start 到 theta_end）
    generate_half_ellipse_arc_points(&points[idx], cell_num-1, x0, y0, a_outer, b_outer, theta_start, theta_end, theta_rot);
    idx += cell_num-1;

    // 直线1：外弧终点到内弧终点（重复外弧终点）
    points[idx].x = points[idx - 1].x;
    points[idx].y = points[idx - 1].y;
    idx++;

    // 内弧点（从 theta_end 到 theta_start，反向）
    generate_half_ellipse_arc_points(&points[idx], cell_num-1, x0, y0, a_inner, b_inner, theta_end, theta_start, theta_rot);
    idx += cell_num-1;

    // 直线2：内弧起点到外弧起点（闭合）
    points[idx].x = points[0].x;
    points[idx].y = points[0].y;
    idx++;
}

// 绘制椭圆扇环轮廓
void draw_annular_fan_ring_with_line(void) {
    // 扇环参数
    lv_coord_t x0 = 400; // 中心x
    lv_coord_t y0 = 400; // 中心y
    lv_coord_t a_outer = 100; // 外弧长轴半径
    lv_coord_t b_outer = 300; // 外弧短轴半径
    lv_coord_t a_inner = 50;  // 内弧长轴半径
    lv_coord_t b_inner = 180;  // 内弧短轴半径
    float theta_start = 0.0f; // 起始角度（0°）
    float theta_end = M_PI;   // 终止角度（180°）
    float theta_rot = M_PI / 4; // 旋转45°

    // 创建line对象
    lv_obj_t *line = lv_line_create(lv_scr_act());
    lv_obj_set_style_line_width(line, 3, 0);
    lv_obj_set_style_line_color(line, lv_color_hex(0x0000FF), 0); // 蓝色线条
    lv_obj_center(line);

    // 生成扇环点
    static lv_point_t points[POINT_COUNT];
    generate_annular_fan_ring_points(points, POINT_COUNT, x0, y0, a_outer, b_outer, a_inner, b_inner, theta_start, theta_end, theta_rot);

    // 设置line点
    lv_line_set_points(line, points, POINT_COUNT);
}

