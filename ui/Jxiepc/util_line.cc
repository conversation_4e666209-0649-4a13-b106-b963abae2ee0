#include "lvgl/src/core/lv_obj.h"
#include "lvgl/src/misc/lv_area.h"
#include "util.h"
#include <iostream>


// lv_obj_t * line;

// static lv_point_t line_points[] = { {5, 5},  {140, 10} };
// static void draw_ticks_event_cb(lv_event_t *e) {
//     lv_draw_ctx_t *draw_ctx = lv_event_get_draw_ctx(e);

//  // 计算直线方向向量和长度
//  float x1 = line_points[0].x, y1 = line_points[0].y;
//  float x2 = line_points[1].x, y2 = line_points[1].y;
//  float dx = x2 - x1;
//  float dy = y2 - y1;
//  float len = sqrt(dx * dx + dy * dy);
//  if (len < 1e-6) return; // 避免除零

//  // 计算法向量（调整为“左上方”）
//  float nx = dy / len; // 法向量 x 分量 (dy, -dx) 取反后为左上方
//  float ny = -dx / len; // 法向量 y 分量
//  if (nx > 0 || ny > 0) { // 确保左上方 (X 减小, Y 减小)
//      nx = -nx;
//      ny = -ny;
//  }

//  // 平移距离
//  float d = 20.0f; // 移动距离 20 像素

//     // 刻度线参数
//     int tick_length = 10; // 刻度线长度
//     int num_ticks = 3;

//     // 设置刻度线样式
//     lv_draw_line_dsc_t tick_dsc;
//     lv_draw_line_dsc_init(&tick_dsc);
//     tick_dsc.color = lv_color_hex(0x000000); // 黑色
//     tick_dsc.width = 2;
//     tick_dsc.opa = LV_OPA_COVER;

//     // 绘制刻度线
//     int num_connectors = 3;
//     for (int i = 0; i < num_connectors; i++) {
//         // 计算原始直线上的等分点
//         float t = (float)i / (num_connectors - i -1); // 插值参数 t 从 0 到 1
//         std::cout << "====> " << t  << " i: "<< i << " num_connectors: " << num_connectors << std::endl;
//         std::cout << "dx: " << dx*t << " dy: " << dy*t << std::endl;
//        std::cout << "x: " << x1 + t * dx << " y: " << y1 + t * dy << std::endl;
   
//         lv_coord_t x = (lv_coord_t)(x1 + t * dx);
//         lv_coord_t y = (lv_coord_t)(y1 + t * dy);
   
//         // 计算对应平移直线上的点
//         lv_coord_t x_shifted = (lv_coord_t)(x + d * nx);
//         lv_coord_t y_shifted = (lv_coord_t)(y + d * ny);
   
   
//         lv_point_t tick_start = {x, y};
//         lv_point_t tick_end = {x_shifted, y_shifted};
//         std::cout << "tick_start: " << tick_start.x << ", " << tick_start.y << std::endl;
     
//         lv_draw_line(draw_ctx, &tick_dsc, &tick_start, &tick_end);
//     }

// }

// void test_line(void)
// {
//     /*Create an array for the points of the line*/

//     /*Create style*/
//     static lv_style_t style_line;
//     lv_style_init(&style_line);
//     lv_style_set_line_width(&style_line, 20);
//     lv_style_set_line_color(&style_line, lv_palette_main(LV_PALETTE_BLUE));
//     lv_style_set_line_rounded(&style_line, true);

//     /*Create a line and apply the new style*/
//     line = lv_line_create(lv_scr_act());
//     lv_line_set_points(line, line_points, 2);     /*Set the points*/
//     lv_obj_center(line);
//     lv_obj_add_event_cb(line, draw_ticks_event_cb, LV_EVENT_DRAW_PART_END, NULL);
// }


// void test_line() {
//  // 原始直线两点
//  static lv_point_t main_points[] = {{50, 50}, {250, 150}}; // 起点 (50,50)，终点 (250,150)
    
//  // 创建原始直线
//  lv_obj_t *main_line = lv_line_create(lv_scr_act());
//  lv_line_set_points(main_line, main_points, 2);
 
//  // 设置原始直线样式
//  static lv_style_t main_style;
//  lv_style_init(&main_style);
//  lv_style_set_line_color(&main_style, lv_color_hex(0x0000FF)); // 蓝色
//  lv_style_set_line_width(&main_style, 3);
//  lv_obj_add_style(main_line, &main_style, 0);

//  // 计算直线方向向量和长度
//  float x1 = main_points[0].x, y1 = main_points[0].y;
//  float x2 = main_points[1].x, y2 = main_points[1].y;
//  float dx = x2 - x1;
//  float dy = y2 - y1;
//  float len = sqrt(dx * dx + dy * dy);
//  if (len < 1e-6) return; // 避免除零

//  // 计算法向量（调整为“左上方”）
//  float nx = dy / len; // 法向量 x 分量 (dy, -dx) 取反后为左上方
//  float ny = -dx / len; // 法向量 y 分量
//  if (nx > 0 || ny > 0) { // 确保左上方 (X 减小, Y 减小)
//      nx = -nx;
//      ny = -ny;
//  }

//  // 平移距离
//  float d = 20.0f; // 移动距离 20 像素

//  // 计算平移后的直线坐标
//  static lv_point_t shifted_points[] = {
//      {(lv_coord_t)(x1 + d * nx), (lv_coord_t)(y1 + d * ny)},
//      {(lv_coord_t)(x2 + d * nx), (lv_coord_t)(y2 + d * ny)}
//  };

//  // 创建平移后的直线
//  lv_obj_t *shifted_line = lv_line_create(lv_scr_act());
//  lv_line_set_points(shifted_line, shifted_points, 2);

//  // 设置平移后直线样式
//  static lv_style_t shifted_style;
//  lv_style_init(&shifted_style);
//  lv_style_set_line_color(&shifted_style, lv_color_hex(0xFF0000)); // 红色
//  lv_style_set_line_width(&shifted_style, 3);
//  lv_obj_add_style(shifted_line, &shifted_style, 0);

//  // 连接线参数
//  int num_connectors = 5; // 连接线数量
//  float connector_spacing = len / (num_connectors - 1); // 连接线间距

//  // 绘制连接线
// static lv_point_t connector_points[2] = {};
//  for (int i = 0; i < num_connectors; i++) {
//      // 计算原始直线上的等分点
//      float t = (float)i / (num_connectors - i -1); // 插值参数 t 从 0 到 1
//      std::cout << "====> " << t  << " i: "<< i << " num_connectors: " << num_connectors << std::endl;
//      std::cout << "dx: " << dx*t << " dy: " << dy*t << std::endl;
//     std::cout << "x: " << x1 + t * dx << " y: " << y1 + t * dy << std::endl;

//      lv_coord_t x = (lv_coord_t)(x1 + t * dx);
//      lv_coord_t y = (lv_coord_t)(y1 + t * dy);

//      // 计算对应平移直线上的点
//      lv_coord_t x_shifted = (lv_coord_t)(x + d * nx);
//      lv_coord_t y_shifted = (lv_coord_t)(y + d * ny);

//      // 创建连接线
//     connector_points[0] = {x, y}; // 原始直线上的点
//     connector_points[1] = {x_shifted, y_shifted}; // 平移直线上的点

//     std::cout << "====> " << connector_points[0].x << ", " << connector_points[0].y << std::endl;
//     std::cout << "====> " << connector_points[1].x << ", " << connector_points[1].y << std::endl;

//      lv_obj_t *connector = lv_line_create(lv_scr_act());
//      lv_line_set_points(connector, connector_points, 2);

//      // 设置连接线样式
//      static lv_style_t connector_style;
//      lv_style_init(&connector_style);
//      lv_style_set_line_color(&connector_style, lv_color_hex(0x00FF00)); // 绿色
//      lv_style_set_line_width(&connector_style, 2);
//      lv_obj_add_style(connector, &connector_style, 0);
//      std::cout << "======================" << std::endl;
//  }
// }



static lv_point_t main_points[] = {{20, 20}, {200, 200}}; // 起点 (50,50)，终点 (250,150)

static void draw_connectors_event_cb(lv_event_t *e) {
    lv_obj_t *line = lv_event_get_target(e);
    lv_draw_ctx_t *draw_ctx = lv_event_get_draw_ctx(e);

    // 计算直线方向向量和长度
    float x1 = main_points[0].x, y1 = main_points[0].y;
    float x2 = main_points[1].x, y2 = main_points[1].y;
    float dx = x2 - x1;
    float dy = y2 - y1;
    float len = sqrt(dx * dx + dy * dy);
    if (len < 1e-6) {
        printf("Error: Line length too small\n");
        return; // 避免除零
    }

    // 计算法向量（调整为“左上方”）
    float nx = dy / len; // 法向量 x 分量 (dy, -dx) 取反后为左上方
    float ny = -dx / len; // 法向量 y 分量
    if (nx > 0 || ny > 0) { // 确保左上方 (X 减小, Y 减小)
        nx = -nx;
        ny = -ny;
    }

    std::cout << "param: " << dx << ", " << dy << std::endl;
    std::cout << "param: " << nx << ", " << ny << std::endl;
    std::cout << "param: " << len << std::endl;

    // 平移距离
    float d = 10.0f; // 移动距离 20 像素

    // 连接线参数
    float connector_spacing = 40.0f; // 每 40 像素一条连接线
    int num_connectors = (int)(len / connector_spacing) + 1; // 动态计算连接线数量
    if (num_connectors < 2) num_connectors = 2; // 至少两条连接线

    // 设置连接线样式
    lv_draw_line_dsc_t connector_dsc;
    lv_draw_line_dsc_init(&connector_dsc);
    connector_dsc.color = lv_color_hex(0x00FF00); // 绿色
    connector_dsc.width = 2;
    connector_dsc.opa = LV_OPA_COVER;

    // 绘制连接线
    for (int i = 0; i < num_connectors; i++) {
        // 计算原始直线上的等分点
        float t = (float)i / (num_connectors - 1); // 插值参数 t 从 0 到 1
        lv_point_t start = {(lv_coord_t)(x1 + t * dx), (lv_coord_t)(y1 + t * dy)};

        // 计算对应平移直线上的点
        lv_point_t end = {(lv_coord_t)(start.x + d * nx), (lv_coord_t)(start.y + d * ny)};

        std::cout << "====> " << start.x << ", " << start.y << "====> " << end.x << ", " << end.y << std::endl;
        // 绘制连接线
        lv_draw_line(draw_ctx, &connector_dsc, &start, &end);
    }
}


void test_line_connectors(void) {
    // 创建原始直线
    lv_obj_t *self = lv_obj_create(lv_scr_act());
    lv_obj_set_size(self, 400, 200);
    lv_obj_t *main_line = lv_line_create(self);
    lv_line_set_points(main_line, main_points, 2);
    
    // 设置原始直线样式
    static lv_style_t main_style;
    lv_style_init(&main_style);
    lv_style_set_line_color(&main_style, lv_color_hex(0x0000FF)); // 蓝色
    lv_style_set_line_width(&main_style, 3);
    lv_obj_add_style(main_line, &main_style, 0);

    // 计算直线方向向量和长度
    float x1 = main_points[0].x, y1 = main_points[0].y;
    float x2 = main_points[1].x, y2 = main_points[1].y;
    float dx = x2 - x1;
    float dy = y2 - y1;
    float len = sqrt(dx * dx + dy * dy);
    if (len < 1e-6) return;

    // 计算法向量（调整为“左上方”）
    float nx = dy / len;
    float ny = -dx / len;
    if (nx > 0 || ny > 0) {
        nx = -nx;
        ny = -ny;
    }

    // 平移距离
    float d = 20.0f;

    // 计算平移后的直线坐标
    // lv_point_t shifted_points[] = {
    //     {(lv_coord_t)(x1 + d * nx), (lv_coord_t)(y1 + d * ny)},
    //     {(lv_coord_t)(x2 + d * nx), (lv_coord_t)(y2 + d * ny)}
    // };

    // 注册绘制回调
    lv_obj_add_event_cb(main_line, draw_connectors_event_cb, LV_EVENT_DRAW_MAIN, NULL);
}
void test_line(void) {
    // test_rect();
    // draw_ellipse_with_line();
    // draw_annular_fan_ring_with_line();
    // draw_chamfered_square_with_line();
    // test_rect();
    // draw_annular_fan_ring_with_line();
    // draw_half_ellipse_arc_with_line();
    // draw_half_ellipse_arc_with_line();
    test_line_connectors();
}