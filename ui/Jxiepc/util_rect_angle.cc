#include "lvgl/lvgl.h"

void test_rect() {

    static lv_point_t points[] = {{50, 50}, {50, 100}, {100, 100}, {100, 50}, {50, 50}}; // 起点 (50,50)，终点 (250,150)
    lv_obj_t *rect = lv_line_create(lv_scr_act());
    lv_line_set_points(rect, points, 5);

    lv_obj_set_style_line_dash_gap(rect, 5, LV_STATE_DEFAULT);
    lv_obj_set_style_line_dash_width(rect, 5, LV_STATE_DEFAULT);

    lv_obj_t* obj = lv_obj_create(lv_scr_act());
    lv_obj_set_size(obj, 400, 200);
    lv_obj_set_style_border_width(obj, 2, LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(obj, lv_color_hex(0x0000FF), LV_STATE_DEFAULT);
    lv_obj_set_style_radius(obj, 100, LV_STATE_DEFAULT);
    lv_obj_align(obj, LV_ALIGN_CENTER, 0, 0);
}