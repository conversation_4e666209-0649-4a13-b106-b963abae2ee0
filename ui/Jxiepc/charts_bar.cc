#include "chart.h"
#include "lvgl/src/extra/widgets/chart/lv_chart.h"
#include <cstdio>
#include <iostream>
#include <ostream>

lv_chart_series_t * ser1;
lv_chart_series_t * ser2;

static void draw_event_cb(lv_event_t * e)
{
    lv_obj_draw_part_dsc_t * dsc = lv_event_get_draw_part_dsc(e);
    // if(!lv_obj_draw_part_check_type(dsc, &lv_chart_class, LV_CHART_DRAW_PART_TICK_LABEL)) return;

    if(dsc->id == LV_CHART_AXIS_PRIMARY_X && dsc->text) {
        const char * month[] = {"Jan", "Febr", "March", "Apr", "May", "Jun", "July", "Aug", "Sept", "Oct", "Nov", "Dec"};
        lv_snprintf(dsc->text, dsc->text_length, "%s", month[dsc->value]);
    }

    
    if (dsc->type == LV_CHART_DRAW_PART_BAR &&
            dsc->part == LV_PART_ITEMS) {
            dsc->rect_dsc->bg_color = lv_color_hex(0x5555FF); // Blue background
            dsc->rect_dsc->bg_opa = LV_OPA_COVER;
            // dsc->rect_dsc->bg_grad_color = lv_color_hex(0xAAAAFF); // Gradient to light blue
            // dsc->rect_dsc->bg_grad.= LV_GRAD_DIR_HOR; // Horizontal gradient
            dsc->rect_dsc->border_color = lv_color_hex(0xFFFF00); // Yellow border
            dsc->rect_dsc->border_width = 3;
            dsc->rect_dsc->border_opa = LV_OPA_60; // 60% border opacity
            dsc->rect_dsc->shadow_color = lv_color_hex(0x000000); // Black shadow
            dsc->rect_dsc->shadow_width = 15;
            dsc->rect_dsc->shadow_ofs_x = 0;
            dsc->rect_dsc->shadow_ofs_y = 10;
            dsc->rect_dsc->shadow_opa = LV_OPA_40; // 40% shadow opacity
            dsc->rect_dsc->radius = 8; // Larger rounded corners

            //dsc->draw_area->x2 += 20;
            if(dsc->id == 0) {
                std::cout << "id value====> " << dsc->id << " " << dsc->value << std::endl;
                std::cout << "x====> " << dsc->draw_area->x1 << " " << dsc->draw_area->x2 << std::endl;
                std::cout << "y====> " << dsc->draw_area->y1 << " " << dsc->draw_area->y2 << std::endl;
            }
    } 
}

void add_data(lv_timer_t * timer)
{
    LV_UNUSED(timer);
    lv_obj_t * chart = (lv_obj_t *)timer->user_data;
    lv_chart_set_next_value(chart, ser1, lv_rand(0, 100));
}

/**
 * Add ticks and labels to the axis and demonstrate scrolling
 */
void test_bar(void)
{
    /*Create a chart*/
    lv_obj_t * chart;
    chart = lv_chart_create(lv_scr_act());
    lv_obj_set_size(chart, 200, 150);
    lv_obj_center(chart);
    lv_chart_set_type(chart, LV_CHART_TYPE_BAR);
    lv_chart_set_update_mode(chart, LV_CHART_UPDATE_MODE_SHIFT);
    lv_chart_set_range(chart, LV_CHART_AXIS_PRIMARY_Y, 0, 100);
    lv_chart_set_range(chart, LV_CHART_AXIS_SECONDARY_Y, 0, 400);
    lv_chart_set_point_count(chart, 3);
    lv_obj_add_event_cb(chart, draw_event_cb, LV_EVENT_DRAW_PART_BEGIN, NULL);

    /*Add ticks and label to every axis*/
    lv_chart_set_axis_tick(chart, LV_CHART_AXIS_PRIMARY_X, 10, 5, 3, 3, true, 40);
    lv_chart_set_axis_tick(chart, LV_CHART_AXIS_PRIMARY_Y, 10, 5, 6, 2, true, 50);
    lv_chart_set_axis_tick(chart, LV_CHART_AXIS_SECONDARY_Y, 10, 5, 3, 4, true, 50);

    /*Zoom in a little in X*/
    /*Add two data series*/
    ser1 = lv_chart_add_series(chart, lv_palette_lighten(LV_PALETTE_GREEN, 2), LV_CHART_AXIS_PRIMARY_Y);
    ser2 = lv_chart_add_series(chart, lv_palette_darken(LV_PALETTE_GREEN, 2),
                                                   LV_CHART_AXIS_SECONDARY_Y);

    /*Set the next points on 'ser1'*/
    lv_chart_set_next_value(chart, ser1, 31);
    lv_chart_set_next_value(chart, ser1, 66);
    lv_chart_set_next_value(chart, ser1, 66);

    lv_coord_t * ser2_array = lv_chart_get_y_array(chart, ser2);
    /*Directly set points on 'ser2'*/
    ser2_array[0] = 92;
    ser2_array[1] = 71;
    ser2_array[2] = 61;

    lv_timer_create(add_data, 1000, chart);

    lv_chart_refresh(chart); /*Required after direct set*/
}