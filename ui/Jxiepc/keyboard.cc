#include "keyboard.h"




static void event_cb(lv_event_t * e)
{
    lv_obj_t * obj = lv_event_get_target(e);
    uint32_t id = lv_btnmatrix_get_selected_btn(obj);
    bool prev = id == 0 ? true : false;
    bool next = id == 6 ? true : false;
    if(prev || next) {
        /*Find the checked button*/
        uint32_t i;
        for(i = 1; i < 80; i++) {
            if(lv_btnmatrix_has_btn_ctrl(obj, i, LV_BTNMATRIX_CTRL_CHECKED)) break;
        }

        if(prev && i > 1) i--;
        else if(next && i < 5) i++;

        lv_btnmatrix_set_btn_ctrl(obj, i, LV_BTNMATRIX_CTRL_CHECKED);
    }
}

/**
 * Make a button group (pagination)
 */
void lv_ex_btnmatrix_1(void)
{
    static lv_style_t style_bg;
    lv_style_init(&style_bg);
    lv_style_set_pad_all(&style_bg, 0);
    lv_style_set_pad_gap(&style_bg, 0);
    lv_style_set_clip_corner(&style_bg, true);
    lv_style_set_radius(&style_bg, LV_RADIUS_CIRCLE);
    lv_style_set_border_width(&style_bg, 0);

    static lv_style_t style_btn;
    lv_style_init(&style_btn);
    lv_style_set_radius(&style_btn, 0);
    lv_style_set_border_width(&style_btn, 1);
    lv_style_set_border_opa(&style_btn, LV_OPA_50);
    lv_style_set_border_color(&style_btn, lv_palette_main(LV_PALETTE_GREY));
    lv_style_set_border_side(&style_btn, LV_BORDER_SIDE_INTERNAL);
    lv_style_set_radius(&style_btn, 0);

    static const char * map[] = {LV_SYMBOL_LEFT, "1", "2", "3", "4", "5", 
//        LV_SYMBOL_AUDIO
//        ,  LV_SYMBOL_VIDEO
//        ,  LV_SYMBOL_LIST
//        ,  LV_SYMBOL_OK
//        ,  LV_SYMBOL_CLOSE
//        ,  LV_SYMBOL_POWER
//        ,  LV_SYMBOL_SETTINGS
//        ,  LV_SYMBOL_HOME
//        ,  LV_SYMBOL_DOWNLOAD
//        ,  LV_SYMBOL_DRIVE
//        ,  LV_SYMBOL_REFRESH
//        ,  LV_SYMBOL_MUTE
//        ,  LV_SYMBOL_VOLUME_MID
//        ,  LV_SYMBOL_VOLUME_MAX
//        ,  LV_SYMBOL_IMAGE
//        ,  LV_SYMBOL_TINT
//        ,  LV_SYMBOL_PREV
//        ,  LV_SYMBOL_PLAY
//        ,  LV_SYMBOL_PAUSE
//        ,  LV_SYMBOL_STOP
//        ,  LV_SYMBOL_NEXT
//        ,  LV_SYMBOL_EJECT
//        ,  LV_SYMBOL_LEFT
//        ,  LV_SYMBOL_RIGHT
//        ,  LV_SYMBOL_PLUS
//        ,  LV_SYMBOL_MINUS
//        ,  LV_SYMBOL_EYE_OPEN
//        ,  LV_SYMBOL_EYE_CLOSE
//        ,  LV_SYMBOL_WARNING
//,  LV_SYMBOL_SHUFFLE
//,  LV_SYMBOL_UP
//,  LV_SYMBOL_DOWN
//,  LV_SYMBOL_LOOP
//,  LV_SYMBOL_DIRECTORY
//,  LV_SYMBOL_UPLOAD
//,  LV_SYMBOL_CALL
//,  LV_SYMBOL_CUT
//,  LV_SYMBOL_COPY
//,  LV_SYMBOL_SAVE
//,  LV_SYMBOL_BARS
//,  LV_SYMBOL_ENVELOPE
//,  LV_SYMBOL_CHARGE
//,  LV_SYMBOL_PASTE
//,  LV_SYMBOL_BELL
//,  LV_SYMBOL_KEYBOARD
//,  LV_SYMBOL_GPS
//,  LV_SYMBOL_USB
//,  LV_SYMBOL_BLUETOOTH
//,  LV_SYMBOL_TRASH
//,  LV_SYMBOL_EDIT
//,  LV_SYMBOL_BACKSPACE
//,  LV_SYMBOL_SD_CARD
//,    LV_SYMBOL_NEW_LINE
//,    LV_SYMBOL_DUMMY,

        LV_SYMBOL_RIGHT, "\xF0\x9F\x91\x8D", ""};


    lv_obj_t * btnm = lv_btnmatrix_create(lv_scr_act());
    lv_obj_set_pos(btnm, 0, 0);
    lv_btnmatrix_set_map(btnm, map);

    lv_obj_set_style_text_font(btnm, &lv_font_montserrat_24, 0);

    
    //lv_obj_add_style(btnm, &style_btn, LV_PART_ITEMS);
    //lv_obj_add_event_cb(btnm, event_cb, LV_EVENT_VALUE_CHANGED, NULL);
    lv_obj_set_size(btnm, 225, 35);

    /*Allow selecting on one number at time*/
    //lv_btnmatrix_set_btn_ctrl_all(btnm, LV_BTNMATRIX_CTRL_CHECKABLE);
    //lv_btnmatrix_clear_btn_ctrl(btnm, 0, LV_BTNMATRIX_CTRL_CHECKABLE);
    //lv_btnmatrix_clear_btn_ctrl(btnm, 6, LV_BTNMATRIX_CTRL_CHECKABLE);

    //lv_btnmatrix_set_one_checked(btnm, true);
    //lv_btnmatrix_set_btn_ctrl(btnm, 1, LV_BTNMATRIX_CTRL_CHECKED);

    // lv_obj_center(btnm);
}
