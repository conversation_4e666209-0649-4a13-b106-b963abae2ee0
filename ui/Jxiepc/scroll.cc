#include "scroll.h"

static uint32_t btn_cnt = 1;

static void float_btn_event_cb(lv_event_t * e)
{
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t * float_btn = lv_event_get_target(e);

    if(code == LV_EVENT_CLICKED) {
        lv_obj_t * list = (lv_obj_t *)lv_event_get_user_data(e);
        char buf[32];
        lv_snprintf(buf, sizeof(buf), "Track %d", (int)btn_cnt);
        lv_obj_t * list_btn = lv_list_add_btn(list, LV_SYMBOL_AUDIO, buf);
        btn_cnt++;

        lv_obj_move_foreground(float_btn);

        // lv_obj_scroll_to_view(list_btn, LV_ANIM_ON);
    }
}

/**
 * Create a list with a floating button
 */
void scroll_test(void)
{
//    lv_obj_set_size(chart, 280, 220);
//    lv_obj_center(chart);
    lv_obj_t * list = lv_obj_create(lv_scr_act());
    lv_obj_set_size(list, 280, 220);
    lv_obj_center(list);

    lv_obj_t* chart = lv_chart_create(list);
    lv_obj_set_size(chart, 280, 100);
    lv_obj_set_pos(chart, 0, 0);
    lv_obj_align(chart, LV_ALIGN_LEFT_MID, 0, 0);

}