#include "chart.h"
#include "lvgl/src/core/lv_event.h"
#include <cstddef>
#include <cstdio>
#include <iostream>
#include "lvgl/src/core/lv_obj.h"
#include "lvgl/src/extra/widgets/chart/lv_chart.h"
#include "lvgl/src/misc/lv_color.h"
#include "lvgl/src/misc/lv_timer.h"
#include <stdio.h>
#include <string.h>
#include <unistd.h>

static lv_obj_t * chart1;
static lv_chart_series_t * ser1;
static lv_chart_series_t * ser2;
static lv_chart_cursor_t * cursor;
static lv_obj_t *label;
static lv_obj_t *slider;  // 滚动条对象，设为全局便于更新
static uint16_t last_id = 0;

// 自动跟踪标志，控制是否自动跟踪最新数据
static bool auto_track = true;
static bool is_change = false;

// 历史数据管理
#define MAX_HISTORY_SIZE 1000                     // 最大历史数据点数量
#define VISIBLE_POINTS 10                         // 可见点数量
static lv_coord_t history_ser1[MAX_HISTORY_SIZE]; // 保存历史数据
static lv_coord_t history_ser2[MAX_HISTORY_SIZE];
static uint32_t history_count = 0;          // 当前历史数据点数量
static uint32_t display_start_idx = 0;      // 当前显示的起始索引
static uint32_t last_display_start_idx = 0; // 上次显示的起始索引
static uint32_t last_history_count = 0;     // 上次数据总数

static uint32_t add_num = 30;

// 函数声明
static void update_chart_display();
static void update_slider_position();


static void draw_event_cb(lv_event_t * e)
{
    lv_obj_t * obj = lv_event_get_target(e);


    /*Add the faded area before the lines are drawn*/
    lv_obj_draw_part_dsc_t * dsc = lv_event_get_draw_part_dsc(e);
    if(dsc->type == LV_CHART_DRAW_PART_LINE_AND_POINT) {
        if(dsc->p1 == NULL || dsc->p2 == NULL || dsc->line_dsc == NULL) {
            return;
        }
        //if(!dsc->p1 || !dsc->p2) return;

        ///*Add a line mask that keeps the area below the line*/
        //lv_draw_mask_line_param_t line_mask_param;
        //lv_draw_mask_line_points_init(&line_mask_param, dsc->p1->x, dsc->p1->y, dsc->p2->x, dsc->p2->y,
        //                              LV_DRAW_MASK_LINE_SIDE_BOTTOM);
        //int16_t line_mask_id = lv_draw_mask_add(&line_mask_param, NULL);

        ///*Add a fade effect: transparent bottom covering top*/
        //lv_coord_t h = lv_obj_get_height(obj);
        //lv_draw_mask_fade_param_t fade_mask_param;
        //lv_draw_mask_fade_init(&fade_mask_param, &obj->coords, LV_OPA_COVER, obj->coords.y1 + h / 8, LV_OPA_TRANSP,
        //                       obj->coords.y2);
        //int16_t fade_mask_id = lv_draw_mask_add(&fade_mask_param, NULL);

        ///*Draw a rectangle that will be affected by the mask*/
        //lv_draw_rect_dsc_t draw_rect_dsc;
        //lv_draw_rect_dsc_init(&draw_rect_dsc);
        //draw_rect_dsc.bg_opa = LV_OPA_80;
        //// draw_rect_dsc.bg_color = dsc->line_dsc->color;
        //draw_rect_dsc.bg_color = lv_palette_main(LV_PALETTE_GREEN);

        //lv_area_t a;
        //a.x1 = dsc->p1->x;
        //a.x2 = dsc->p2->x - 1;
        //a.y1 = LV_MIN(dsc->p1->y, dsc->p2->y);
        //a.y2 = obj->coords.y2;
        //lv_draw_rect(dsc->draw_ctx, &draw_rect_dsc, &a);

        ///*Remove the masks*/
        //lv_draw_mask_free_param(&line_mask_param);
        //lv_draw_mask_free_param(&fade_mask_param);
        //lv_draw_mask_remove_id(line_mask_id);
        //lv_draw_mask_remove_id(fade_mask_id);
        std::cout << "dsc->id: " << dsc->id << std::endl;
        lv_chart_series_t * series = (lv_chart_series_t *)dsc->sub_part_ptr;

        if(series == ser1) {
            dsc->rect_dsc->radius = LV_RADIUS_CIRCLE;
            dsc->rect_dsc->bg_color = lv_palette_main(LV_PALETTE_RED);
            dsc->rect_dsc->bg_opa = LV_OPA_COVER;
            dsc->line_dsc->width = 8;

            // 设置大小
            dsc->draw_area->x1 -= 8;
            dsc->draw_area->y1 -= 8;
            dsc->draw_area->x2 += 8;
            dsc->draw_area->y2 += 8;
        }else {
            dsc->rect_dsc->radius = 0;
            dsc->rect_dsc->bg_color = lv_palette_main(LV_PALETTE_GREEN);
            dsc->rect_dsc->bg_opa = LV_OPA_COVER;

            //// 更大一点
            //dsc->draw_area->x1 -= 4;
            //dsc->draw_area->y1 -= 4;
            //dsc->draw_area->x2 += 4;
            //dsc->draw_area->y2 += 4;
        }
    } else if(dsc->part == LV_PART_MAIN) {
        if(dsc->line_dsc == NULL || dsc->p1 == NULL || dsc->p2 == NULL) return;
        if(dsc->p1->x == dsc->p2->x) {
            dsc->line_dsc->color  = lv_palette_lighten(LV_PALETTE_GREEN, 1);
            if(dsc->id == 1) {
                dsc->line_dsc->width  = 2;
                dsc->line_dsc->dash_gap  = 0;
                dsc->line_dsc->dash_width  = 0;
            }
            else {
                dsc->line_dsc->width = 1;
                dsc->line_dsc->dash_gap  = 2;
                dsc->line_dsc->dash_width  = 6;
                dsc->line_dsc->raw_end = true;
            }
        }
        /*Horizontal line*/
        else {
            if(dsc->id == 2) {
                dsc->line_dsc->width  = 2;
                dsc->line_dsc->dash_gap  = 0;
                dsc->line_dsc->dash_width  = 0;
            }
            else {
                dsc->line_dsc->width = 2;
                dsc->line_dsc->dash_gap  = 6;
                dsc->line_dsc->dash_width  = 6;
            }

            if(dsc->id == 1  || dsc->id == 3) {
                dsc->line_dsc->color  = lv_palette_main(LV_PALETTE_GREEN);
            }
            else {
                dsc->line_dsc->color  = lv_palette_lighten(LV_PALETTE_GREY, 1);
            }
        }
    }

    // if(dsc->part == LV_PART_TICKS && dsc->text) {
    if(dsc->type == LV_CHART_DRAW_PART_TICK_LABEL) {
        // 自定义X轴标签文本，显示实际的数据点索引
        if(dsc->text && dsc->id == LV_CHART_AXIS_PRIMARY_X) {
            // std::cout << "dsc->text: " << dsc->text << " dsc->id: " << dsc->id << " dsc->value: " << dsc->value << std::endl;
            lv_draw_label_dsc_t * label_dsc = dsc->label_dsc;
            label_dsc->color = lv_palette_main(LV_PALETTE_GREEN);
            uint32_t idx = display_start_idx + dsc->value;
            // 修改标签文本为数据点索引
            char buf[16];
            snprintf(buf, sizeof(buf), "name: %d", idx);
            strcpy(dsc->text, buf);
        }
    }
}

// 添加新数据并存储到历史记录
static void add_data(lv_timer_t * timer)
{
    if(add_num <= 0) {
        return;
    }
    LV_UNUSED(timer);
    static uint32_t cnt = 0;
    
    // 生成新数据
    lv_coord_t val1 = cnt;
    lv_coord_t val2 = cnt % 4 == 0 ? lv_rand(40, 60) : history_ser2[history_count > 0 ? history_count-1 : 0];
    
    // 保存到历史数据
    if(history_count < MAX_HISTORY_SIZE) {
        history_ser1[history_count] = val1;
        history_ser2[history_count] = val2;
        history_count++;
    } else {
        // 历史数据满了，移动数据
        for(uint32_t i = 0; i < MAX_HISTORY_SIZE - 1; i++) {
            history_ser1[i] = history_ser1[i + 1];
            history_ser2[i] = history_ser2[i + 1];
        }
        history_ser1[MAX_HISTORY_SIZE - 1] = val1;
        history_ser2[MAX_HISTORY_SIZE - 1] = val2;
    }
    
    // 如果是自动跟踪模式，调整显示区域到最新数据
    if(auto_track) {
        display_start_idx = history_count > VISIBLE_POINTS ? history_count - VISIBLE_POINTS : 0;
    }
    
    add_num--;

    // lv_chart_set_next_value 默认是从右侧（最新点）依次添加数据，如果想从左侧（最旧点）开始设置，可以使用 lv_chart_set_value_by_id
    // 例如，依次从左到右填充数据点（假设每次都从0号点开始）：
    static uint16_t left_insert_idx = 0;
//     lv_chart_set_value_by_id(chart1, ser1, left_insert_idx, val1);
    if(left_insert_idx < lv_chart_get_point_count(chart1)) {
        lv_chart_set_value_by_id(chart1, ser1, left_insert_idx, val1);
    }else {
        lv_chart_set_next_value(chart1, ser1, val1);
    }
    left_insert_idx++;
    // if(left_insert_idx >= lv_chart_get_point_count(chart1)) left_insert_idx = 0; // 循环覆盖

    // 性能优化建议：
    // 如果你需要频繁地从左侧批量设置数据（如初始化或大批量更新），建议使用 lv_chart_set_all_value 或者一次性批量设置数据数组，
    // 避免每次都调用 set_value_by_id 导致多次重绘。可以先批量写入数据，再调用 lv_chart_refresh 一次性刷新图表，这样能显著减少重绘次数，提高性能。
    // 拓展知识点：
    // lv_chart_set_next_value 适合实时数据流（如心电、波形等），自动将新数据推入右侧，左侧数据依次左移。
    // lv_chart_set_value_by_id 适合自定义任意位置的数据点，灵活性更高，但需要手动管理索引。
    // lv_chart_set_all_value 可一次性重置所有点，适合全量刷新。

    //// 更新图表内容宽度，使其可以容纳所有数据点并保持滚动
    //lv_coord_t point_width = 400 / VISIBLE_POINTS; // 每个点的宽度
    //lv_coord_t chart_width = history_count * point_width;
    
    //// 确保内容宽度至少与图表宽度相同
    //if(chart_width < lv_obj_get_width(chart1)) {
    //    chart_width = lv_obj_get_width(chart1);
    //}
    
    //lv_obj_set_content_width(chart1, chart_width);
    
    //// 如果是自动跟踪模式，滚动到最新数据
    //if(auto_track && history_count > VISIBLE_POINTS) {
    //    lv_coord_t max_scroll = lv_obj_get_content_width(chart1) - lv_obj_get_width(chart1);
    //    if(max_scroll > 0) {
    //        lv_obj_scroll_to_x(chart1, max_scroll, LV_ANIM_ON);
    //    }
    //}

    // 更新图表显示
    // update_chart_display();
    
    // 更新滑块位置，与图表数据保持一致
    // update_slider_position();
    
    cnt++;
}


static void update_chart_all_display() {
    uint16_t point_count = lv_chart_get_point_count(chart1);
    
    lv_chart_set_all_value(chart1, ser1, 0);
    lv_chart_set_all_value(chart1, ser2, 0);
        
    for(uint16_t i = 0; i < point_count; i++) {
        uint32_t idx = display_start_idx + i;
        if(idx < history_count) {
            lv_chart_set_value_by_id(chart1, ser1, i, history_ser1[idx]);
            lv_chart_set_value_by_id(chart1, ser2, i, history_ser2[idx]);
        }
    }
    
    // 刷新图表
    lv_chart_refresh(chart1);
}

static void update_chart_display_sliding() {
    uint16_t point_count = lv_chart_get_point_count(chart1);
    
    // 计算显示索引差异
    int diff = display_start_idx - last_display_start_idx;
    
    // std::cout << "diff: " << diff << " point_count: " << point_count << " last_history_count: " << last_history_count << " history_count: " << history_count << " display_start_idx: " << display_start_idx << " last_display_start_idx: " << last_display_start_idx << std::endl;
    // 无变化或数据总量变化时使用全量更新
    if(diff == 0 || last_history_count != history_count) {
        // update_chart_all_display();
    }else {
        // 使用点滑动方法进行增量更新
        if(abs(diff) < point_count) {
            // 滑动方向不同处理
            if(diff > 0) {
                // 向左滑动：移除左侧点，添加右侧新点
                std::cout << "向左滑动" << std::endl;
                for(int i = 0; i < diff; i++) {

                    // 添加右侧新点
                    uint32_t new_idx = display_start_idx + point_count - 1 - i;
                    if(new_idx < history_count) {
                        lv_chart_set_next_value(chart1, ser1, history_ser1[new_idx]);
                        lv_chart_set_next_value(chart1, ser2, history_ser2[new_idx]);
                    } else {
                        lv_chart_set_next_value(chart1, ser1, 0);
                        lv_chart_set_next_value(chart1, ser2, 0);
                    }
                }
            } else {
                // 向右滑动：移除右侧点，添加左侧新点
                diff = -diff; // 转为正数
                std::cout << "向右滑动: " << diff << std::endl;
                for(int i = 0; i < diff; i++) {
                    // 在开头添加新点
                    uint32_t new_idx = display_start_idx + i;
                    if(new_idx < history_count) {
                        lv_chart_set_value_by_id(chart1, ser1, i, history_ser1[new_idx]);
                        lv_chart_set_value_by_id(chart1, ser2, i, history_ser2[new_idx]);
                    } else {
                        lv_chart_set_value_by_id(chart1, ser1, i, 0);
                        lv_chart_set_value_by_id(chart1, ser2, i, 0);
                    }
                }
            }
        } else {
            // 差异太大，使用全量更新
            // update_chart_all_display();
        }
    }
    
    // 更新记录的状态
    last_display_start_idx = display_start_idx;
    last_history_count = history_count;
}

// 更新图表显示
static void update_chart_display() {
    update_chart_display_sliding();
    //uint16_t point_count = lv_chart_get_point_count(chart1);
    
    //// 计算可见数据的范围
    //uint16_t display_count = history_count < point_count ? history_count : point_count;
    
    //// 检查是否需要完全刷新
    //bool need_full_refresh = false;
    
    //// 如果显示的起始索引变化了超过1个点，或者数据总量发生变化，需要完全刷新
    //if(last_display_start_idx != display_start_idx || last_history_count != history_count) {
    //    need_full_refresh = true;
    //}
    
    //if(need_full_refresh) {
    //    // 需要完全刷新时清空并重新填充所有点
    //    lv_chart_set_all_value(chart1, ser1, 0);
    //    lv_chart_set_all_value(chart1, ser2, 0);
    //    
    //    for(uint16_t i = 0; i < display_count; i++) {
    //        uint32_t idx = display_start_idx + i;
    //        if(idx < history_count) {
    //            lv_chart_set_value_by_id(chart1, ser1, i, history_ser1[idx]);
    //            lv_chart_set_value_by_id(chart1, ser2, i, history_ser2[idx]);
    //        }
    //    }
    //} else if(auto_track && history_count > 0) {
    //    // 自动跟踪模式下，只需更新最新的点
    //    uint16_t pos = display_count - 1;
    //    lv_chart_set_value_by_id(chart1, ser1, pos, history_ser1[history_count - 1]);
    //    lv_chart_set_value_by_id(chart1, ser2, pos, history_ser2[history_count - 1]);
    //}
    
    //// 刷新图表
    //lv_chart_refresh(chart1);
    
    //// 更新记录的状态
    //last_display_start_idx = display_start_idx;
    //last_history_count = history_count;
    // usleep(100000);
}

// 更新滑块位置以匹配当前显示区域
static void update_slider_position() {

    if(!slider) return;
    
    // 避免数据为0时的除零错误
    if(history_count <= VISIBLE_POINTS) {
        lv_slider_set_value(slider, 100, LV_ANIM_ON);
        return;
    }
    
    // 计算滑块位置百分比 = 当前显示位置 / 最大可能位置 * 100
    uint32_t max_start_idx = history_count - VISIBLE_POINTS;
    int32_t slider_value = (display_start_idx * 100) / max_start_idx;
    
    // 限制在0-100之间
    if(slider_value < 0) slider_value = 0;
    if(slider_value > 100) slider_value = 100;
    
    // 设置滑块位置，带动画效果
    lv_slider_set_value(slider, slider_value, LV_ANIM_ON);
}

static void chart_event_cb(lv_event_t *e) {

    lv_obj_t *obj = lv_event_get_target(e);

    if(e->code == LV_EVENT_VALUE_CHANGED) {
        last_id = lv_chart_get_pressed_point(obj);
        if(last_id != LV_CHART_POINT_NONE) {
            lv_chart_set_cursor_point(obj, cursor, NULL, last_id);
        }
    } else if(e->code == LV_EVENT_DRAW_PART_END) {
        lv_chart_series_t *tmp_ser= lv_chart_get_series_next(obj, NULL);
        lv_coord_t * data_array = lv_chart_get_y_array(obj, tmp_ser);
        lv_coord_t value = data_array[last_id];
        char buf[32];
        snprintf(buf, sizeof(buf), "Point: %d, Value: %d", last_id, value);
        lv_label_set_text(label, buf);
        lv_obj_align_to(label, obj, LV_ALIGN_OUT_TOP_MID, 0, -10);
    }

    // 更新标签
}

// 滚动条事件回调
static void slider_event_cb(lv_event_t * e) {
    is_change = true;
    // 获取事件代码
    lv_event_code_t code = lv_event_get_code(e);
    
    static bool is_dragging = false;
    static lv_point_t last_point = {0, 0};
    static lv_coord_t y_axis = 220 / 100;
    // 如果是拖拽事件
    if(code == LV_EVENT_PRESSING) {
        auto_track = false;
        // 获取拖拽的距离
        lv_point_t point;
        lv_indev_get_point(lv_indev_get_act(), &point);
        std::cout << "act device: " << lv_indev_get_act()->driver->type << std::endl;
        
        if(last_point.x == 0 && last_point.y == 0) {
            last_point = point;
            return;
        }
        // 计算相对于上一次位置的偏移
        lv_coord_t dx = point.x - last_point.x;
        // lv_coord_t dy = point.y - last_point.y;
        
        //std::cout << "dx: " << dx << " point.x: " << point.x << " last_point.x: " << last_point.x << std::endl;
        // 记录当前位置为下一次计算的基准
        
        // 根据拖拽距离更新显示
        // 处理水平方向拖拽（X轴）
        if(dx != 0) {
            // 计算新的显示起始索引
            int32_t points_to_move = dx / 10; // 每10像素移动一个数据点
            int32_t new_start_idx = display_start_idx - points_to_move;
            
            // 确保索引在有效范围内
            if(new_start_idx >= 0 && new_start_idx <= (int32_t)(history_count - VISIBLE_POINTS)) {
                display_start_idx = new_start_idx;
                std::cout << "display_start_idx: " << display_start_idx << std::endl;
                update_chart_display();
                if(!is_dragging) {
                    is_dragging = true;
                    lv_timer_create([](lv_timer_t * timer) {
                        auto_track = true;
                        is_dragging = false;
                        lv_timer_del(timer);
                    }, 5000, NULL);
                }
            }
        }
        
        // 获取当前Y轴范围
        static lv_coord_t y_min = 0, y_max = 100;
           
        std::cout << "point.y: " << point.y << " last_point.y: " << last_point.y << std::endl;

        if(last_point.y > point.y) {
           // 更新Y轴范围，保持范围差值不变
           int dy = (last_point.y - point.y)/2;
           y_min += dy;
           y_max += dy;
           
           std::cout << "Y轴范围更新: " << y_min << " - " << y_max << " dy: " << dy << std::endl;
           // 应用新的Y轴范围
           lv_chart_set_range(chart1, LV_CHART_AXIS_PRIMARY_Y, y_min, y_max);
           auto_track = false;
       }else {
           int dy = (last_point.y - point.y)/2;
           y_min += dy;
           y_max += dy;
           std::cout << "Y轴范围更新: " << y_min << " - " << y_max << " dy: " << dy << std::endl;
           // 应用新的Y轴范围
           lv_chart_set_range(chart1, LV_CHART_AXIS_PRIMARY_Y, y_min, y_max);
           
           auto_track = false;
       }
       printf("last_point: %d, point: %d\n", last_point.y, point.y);
        last_point = point;
    }else if(code == LV_EVENT_RELEASED) {
        auto_track = true;
        last_point = {0, 0};
    }
    
    //// 更新图表显示
    //update_chart_display();
}

void print_obj_flags(lv_obj_t * obj) {
    if(lv_obj_has_flag(obj, LV_OBJ_FLAG_HIDDEN)) {
        printf("hidden\n");
    }
    if (lv_obj_has_flag(obj, LV_OBJ_FLAG_CLICKABLE)) {
        printf("clickable\n");
    }
    if (lv_obj_has_flag(obj, LV_OBJ_FLAG_SCROLLABLE)) {
        printf("scrollable\n");
    }
    if (lv_obj_has_flag(obj, LV_OBJ_FLAG_SCROLL_ELASTIC)) {
        printf("scroll_elastic\n");
    }
    if (lv_obj_has_flag(obj, LV_OBJ_FLAG_SCROLL_MOMENTUM)) {
        printf("scroll_momentum\n");
    }
    if (lv_obj_has_flag(obj, LV_OBJ_FLAG_CHECKABLE)) {
        printf("checkable\n");
    }
    if (lv_obj_has_flag(obj, LV_OBJ_FLAG_PRESS_LOCK)) {
        printf("press_lock\n");
    }
    if (lv_obj_has_flag(obj, LV_OBJ_FLAG_EVENT_BUBBLE)) {
        printf("event_bubble\n");
    }
    if (lv_obj_has_flag(obj, LV_OBJ_FLAG_GESTURE_BUBBLE)) {
        printf("gesture_bubble\n");
    }
    if (lv_obj_has_flag(obj, LV_OBJ_FLAG_ADV_HITTEST)) {
        printf("adv_hittest\n");
    }
    if (lv_obj_has_flag(obj, LV_OBJ_FLAG_IGNORE_LAYOUT)) {
        printf("ignore_layout\n");
    }
    if (lv_obj_has_flag(obj, LV_OBJ_FLAG_FLOATING)) {
        printf("floating\n");
    }
    if (lv_obj_has_flag(obj, LV_OBJ_FLAG_WIDGET_1)) {
        printf("widget_1\n");
    }
    if (lv_obj_has_flag(obj, LV_OBJ_FLAG_WIDGET_2)) {
        printf("widget_2\n");
    }
    if (lv_obj_has_flag(obj, LV_OBJ_FLAG_USER_1)) {
        printf("user_1\n");
    }
}

void chart_scroll() {
    /*创建图表*/
    chart1 = lv_chart_create(lv_scr_act());
    lv_obj_set_size(chart1, 400, 220);
    lv_obj_align(chart1, LV_ALIGN_TOP_MID, 0, 10);
    
    // 设置图表类型和属性
    lv_chart_set_type(chart1, LV_CHART_TYPE_LINE);
    lv_chart_set_point_count(chart1, VISIBLE_POINTS);  // 显示固定数量的点
    // 设置X轴和Y轴的刻度
    lv_chart_set_axis_tick(chart1, LV_CHART_AXIS_PRIMARY_Y, 10, 5, 10, 2, true, 50);
    lv_chart_set_axis_tick(chart1, LV_CHART_AXIS_SECONDARY_Y, 10, 5, 10, 2, true, 50);
    lv_chart_set_axis_tick(chart1, LV_CHART_AXIS_PRIMARY_X, 10, 5, 10, 2, true, 40);

    print_obj_flags(chart1);
    // 滚动条
    //lv_obj_add_flag(chart1, LV_OBJ_FLAG_SCROLLABLE);     // 使图表可滚动
    //lv_obj_set_scroll_dir(chart1, LV_DIR_HOR);           // 只允许水平滚动
    //lv_obj_set_scrollbar_mode(chart1, LV_SCROLLBAR_MODE_AUTO); // 自动显示滚动条
    // lv_chart_set_zoom_x(chart1, 500);
    
    // lv_obj_add_event_cb(chart1, chart_scroll_event_cb, LV_EVENT_SCROLL, NULL);
    // lv_obj_add_event_cb(chart1, chart_scroll_event_cb, LV_EVENT_SCROLL_BEGIN, NULL);
    
    // 启用网格线和刻度
    lv_chart_set_div_line_count(chart1, 0, 20); // X轴和Y轴的分割线数量
    
    // 添加事件回调
    // lv_obj_add_event_cb(chart1, draw_event_cb, LV_EVENT_DRAW_PART_BEGIN, NULL);
    // TODO
    lv_chart_set_update_mode(chart1, LV_CHART_UPDATE_MODE_SHIFT);

    /*添加两个数据序列*/
    ser1 = lv_chart_add_series(chart1, lv_palette_main(LV_PALETTE_RED), LV_CHART_AXIS_PRIMARY_Y);
    ser2 = lv_chart_add_series(chart1, lv_palette_main(LV_PALETTE_BLUE), LV_CHART_AXIS_SECONDARY_Y);

    /// 修改点
    //static lv_style_t style_chart_points;
    //lv_style_init(&style_chart_points);

    ///* 2. 设置数据点的大小 */
    //// 将点的宽度和高度都设置为 8px (即直径为 8px)
    //lv_style_set_width(&style_chart_points, 18);
    //lv_style_set_height(&style_chart_points, 18);
    //lv_style_set_border_width(&style_chart_points, 2);
    //lv_style_set_border_color(&style_chart_points, lv_palette_main(LV_PALETTE_GREEN));
    ///* 3. 使数据点变为圆形 */
    //// LV_RADIUS_CIRCLE 会使点的半径为其短边的一半，从而形成圆形
    //lv_style_set_radius(&style_chart_points, 0);

    ///* 4. 确保数据点是完全不透明的 (如果需要，通常默认如此) */
    //// 如果不设置背景颜色，点通常会采用其所属系列的颜色
    //lv_style_set_text_color(&style_chart_points, lv_palette_main(LV_PALETTE_GREEN));
    //// lv_style_set_bg_opa(&style_chart_points, LV_OPA_COVER);
    ///* 5. 将样式应用到图表的 LV_PART_INDICATOR 部分 */
    //// 这会影响图表上所有系列的数据点
    //lv_obj_add_style(chart1, &style_chart_points, LV_PART_INDICATOR);

    //static lv_style_t style_dashed_line; // 使用 static 或确保样式在图表生命周期内有效
    //lv_style_init(&style_dashed_line);
    cursor = lv_chart_add_cursor(chart1, lv_palette_main(LV_PALETTE_GREEN), LV_CHART_AXIS_PRIMARY_Y);

    ///* 3. 设置虚线属性 */
    //lv_style_set_line_dash_width(&style_dashed_line, 8);  // 短划线长度 8px
    //lv_style_set_line_dash_gap(&style_dashed_line, 4);   // 间隙长度 4px

    ///* 4. (可选) 设置线条宽度 */
    //lv_style_set_line_width(&style_dashed_line, 2);      // 线条粗细 2px

    ///* 5. 将样式应用到图表的 LV_PART_ITEMS 部分 */
    //// 这会影响图表上所有系列的线条
    //lv_obj_add_style(chart1, &style_dashed_line, LV_PART_ITEMS);

    // 设置Y轴范围
    lv_chart_set_range(chart1, LV_CHART_AXIS_PRIMARY_Y, 0, 100);
    lv_chart_set_range(chart1, LV_CHART_AXIS_PRIMARY_X, 0, VISIBLE_POINTS);
    
    
    /*创建导航控件*/
    // 创建滑块
    //slider = lv_slider_create(lv_scr_act());
    //lv_obj_set_size(slider, 280, 20);
    //lv_obj_align_to(slider, chart1, LV_ALIGN_TOP_MID, 0, 230);
    lv_obj_add_event_cb(chart1, slider_event_cb, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(chart1, chart_event_cb, LV_EVENT_ALL, NULL);
    
    //// 设置滑块初始位置为最右侧
    //lv_slider_set_value(slider, 100, LV_ANIM_OFF);
    
    // 添加定时器周期性添加数据
    lv_timer_create(add_data, 500, NULL);
}

