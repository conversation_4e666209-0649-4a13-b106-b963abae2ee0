#include "lvgl/src/core/lv_obj.h"
#include "lvgl/src/misc/lv_color.h"
#include "util.h"
#include <cmath>



void test_canvas(void) {
 /* 创建静态缓冲区，用于存储画布内容 */

     /* 创建画布并设置缓冲区 */
     lv_obj_t *canvas = lv_canvas_create(lv_scr_act());
    static lv_color_t cbuf[LV_CANVAS_BUF_SIZE_TRUE_COLOR(200, 200)];
     lv_canvas_set_buffer(canvas, cbuf, 200, 200, LV_IMG_CF_TRUE_COLOR);
     lv_canvas_fill_bg(canvas, lv_color_hex(0xFFFFFF), LV_OPA_COVER); // 设置白色背景
     lv_obj_center(canvas); // 居中显示

     /* 1. 绘制矩形 */
     lv_draw_rect_dsc_t rect_dsc;
     lv_draw_rect_dsc_init(&rect_dsc);
     rect_dsc.bg_color = lv_palette_main(LV_PALETTE_BLUE);
     rect_dsc.border_color = lv_palette_main(LV_PALETTE_BLUE);
     rect_dsc.border_width = 2;
     lv_area_t rect_coords = {10, 10, 60, 40}; // x1, y1, x2, y2
     lv_canvas_draw_rect(canvas, rect_coords.x1, rect_coords.y1, rect_coords.x2 - rect_coords.x1, rect_coords.y2 - rect_coords.y1, &rect_dsc);

     /* 2. 绘制正方形（矩形的一种，宽高相等） */
     lv_draw_rect_dsc_t square_dsc;
     lv_draw_rect_dsc_init(&square_dsc);
     square_dsc.bg_color = lv_palette_main(LV_PALETTE_RED);
     square_dsc.border_color = lv_palette_main(LV_PALETTE_RED);
     square_dsc.border_width = 2;
     square_dsc.radius = 10;
     lv_area_t square_coords = {80, 10, 110, 40}; // 宽高均为30
     lv_canvas_draw_rect(canvas, square_coords.x1, square_coords.y1, square_coords.x2 - square_coords.x1, square_coords.y2 - square_coords.y1, &square_dsc);

     /* 3. 绘制弧线 */
     lv_draw_arc_dsc_t arc_dsc;
     lv_draw_arc_dsc_init(&arc_dsc);
     arc_dsc.color = lv_palette_main(LV_PALETTE_GREEN);
     arc_dsc.width = 5;
     lv_canvas_draw_arc(canvas, 150, 30, 20, 0, 270, &arc_dsc); // center_x, center_y, radius, start_angle, end_angle

     /* 4. 绘制直线 */
     lv_draw_line_dsc_t line_dsc;
     lv_draw_line_dsc_init(&line_dsc);
     line_dsc.color = lv_palette_main(LV_PALETTE_LIGHT_BLUE);
     line_dsc.width = 3;
     lv_point_t points[] = {{10, 60}, {60, 80}};
     lv_canvas_draw_line(canvas, points, 2, &line_dsc);

     /* 5. 绘制多边形（以五边形为例） */
     lv_draw_line_dsc_t poly_dsc;
     lv_draw_line_dsc_init(&poly_dsc);
     poly_dsc.color = lv_palette_main(LV_PALETTE_PURPLE);
     poly_dsc.width = 2;
     lv_point_t poly_points[] = {
         {80, 60}, {95, 50}, {110, 60}, {105, 80}, {85, 80}
     };
     for (int i = 0; i < 5; i++) {
         lv_point_t line_points[] = {poly_points[i], poly_points[(i + 1) % 5]};
         lv_canvas_draw_line(canvas, line_points, 2, &poly_dsc);
     }

     /* 6. 绘制扇形（通过多边形近似） */
     lv_draw_rect_dsc_t sector_dsc;
     lv_draw_rect_dsc_init(&sector_dsc);
     sector_dsc.bg_color = lv_palette_main(LV_PALETTE_ORANGE);
     sector_dsc.border_color = lv_palette_main(LV_PALETTE_ORANGE);
     sector_dsc.border_width = 2;
     lv_point_t sector_points[10];
     sector_points[0].x = 150; // 圆心
     sector_points[0].y = 80;
     int num_points = 9; // 圆心 + 8个弧线点
     for (int i = 0; i < num_points - 1; i++) {
         float angle = (i * (M_PI / 2) / (num_points - 2)); // 0到90度
         sector_points[i + 1].x = 150 + 20 * cos(angle);
         sector_points[i + 1].y = 80 + 20 * sin(angle);
     }
     lv_canvas_draw_polygon(canvas, sector_points, num_points, &sector_dsc);
}


// // 定义贝塞尔曲线控制点
// static lv_point_t control_points[4] = {
//     {50, 200},  // 起点
//     {100, 50},  // 控制点1
//     {300, 50},  // 控制点2
//     {350, 200}  // 终点
// };

// // 计算三次贝塞尔曲线上的点
// static void calculate_bezier_point(float t, lv_point_t *out, lv_point_t *p0, lv_point_t *p1, lv_point_t *p2, lv_point_t *p3) {
//     float u = 1 - t;
//     float tt = t * t;
//     float uu = u * u;
//     float uuu = uu * u;
//     float ttt = tt * t;

//     float x = uuu * p0->x + 3 * uu * t * p1->x + 3 * u * tt * p2->x + ttt * p3->x;
//     float y = uuu * p0->y + 3 * uu * t * p1->y + 3 * u * tt * p2->y + ttt * p3->y;

//     out->x = (lv_coord_t)x;
//     out->y = (lv_coord_t)y;
// }

// // 动画回调函数，用于动态更新控制点
// static void anim_control_point_cb(void *var, int32_t v) {
//     lv_obj_t *line = (lv_obj_t *)var;
//     static lv_point_t points[100]; // 存储贝塞尔曲线上的点
//     int num_points = 100;

//     // 更新控制点（例如让控制点1在y轴上移动）
//     control_points[1].y = 50 + v;

//     // 计算贝塞尔曲线上的点
//     for (int i = 0; i < num_points; i++) {
//         float t = (float)i / (num_points - 1);
//         calculate_bezier_point(t, &points[i], &control_points[0], &control_points[1], &control_points[2], &control_points[3]);
//     }

//     // 更新line控件的点
//     lv_line_set_points(line, points, num_points);
// }

// // 初始化和绘制贝塞尔曲线
// void test_canvas(void) {
//     // 创建line对象
//     lv_obj_t *line = lv_line_create(lv_scr_act());
//     lv_obj_set_style_line_width(line, 3, 0);
//     lv_obj_set_style_line_color(line, lv_color_hex(0xFF0000), 0); // 红色线条
//     lv_obj_center(line);

//     // 初始化贝塞尔曲线点
//     static lv_point_t points[100];
//     int num_points = 100;

//     for (int i = 0; i < num_points; i++) {
//         float t = (float)i / (num_points - 1);
//         calculate_bezier_point(t, &points[i], &control_points[0], &control_points[1], &control_points[2], &control_points[3]);
//     }

//     // 设置line控件的点
//     lv_line_set_points(line, points, num_points);

//     // 创建动画以动态调整控制点
//     lv_anim_t anim;
//     lv_anim_init(&anim);
//     lv_anim_set_var(&anim, line);
//     lv_anim_set_values(&anim, 0, 150); // 控制点y轴移动范围
//     lv_anim_set_time(&anim, 2000); // 动画时长2秒
//     lv_anim_set_exec_cb(&anim, anim_control_point_cb);
//     lv_anim_set_repeat_count(&anim, LV_ANIM_REPEAT_INFINITE); // 无限循环
//     lv_anim_start(&anim);
// }

// 使用纯线条绘制带虚线边框和倒角的正方形