#include "keyboard.h"
#include "lvgl/src/extra/others/ime/lv_ime_pinyin.h"



static const char *kb_map_lower[] = {
    "1", "22", "33", "4", "5", "6", "7", "8", "9", "0", "\n",
    "qq", "w", "e", "r", "t", "y", "u", "i", "o", "p", "\n",
    "a", "s", "d", "f", "g", "h", "j", "k", "l", "\n",
    "Caps", "z", "x", "c", "v", "b", "n", "m", LV_SYMBOL_BACKSPACE, "\n",
    "Tab", "Space", "Clear", LV_SYMBOL_OK, ""
};

static const char *kb_map_upper[] = {
    "1", "2", "3", "4", "5", "6", "7", "8", "9", "0", "\n",
    "Q", "W", "E", "R", "T", "Y", "U", "I", "O", "P", "\n",
    "A", "S", "D", "F", "G", "H", "J", "K", "L", "\n",
    "Caps", "Z", "X", "C", "V", "B", "N", "M", LV_SYMBOL_BACKSPACE, "\n",
    "Tab", "Space", "Clear", LV_SYMBOL_OK, ""
};

static bool s_flag = false; 
struct user_data{
    lv_obj_t *label = nullptr;
    lv_obj_t *show = nullptr;
};

static void kb_event_cb(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t *kb = lv_event_get_target(e);
    user_data *data = (user_data*)lv_event_get_user_data(e);

    if (code == LV_EVENT_VALUE_CHANGED) {
        const char *txt = lv_btnmatrix_get_btn_text(kb, lv_btnmatrix_get_selected_btn(kb));
        if (!txt) return;
    std::cout << "====> INPUT: " << txt << std::endl;
        // if (strcmp(txt, LV_SYMBOL_BACKSPACE) == 0) {
        //     lv_textarea_del_char(data->show);
        // } else if (strcmp(txt, "Clear") == 0) {
        //     lv_textarea_set_text(data->show, "");
        // } else if (strcmp(txt, "Space") == 0) {
        //     lv_textarea_add_text(data->show, " ");
        // } else if (strcmp(txt, "Tab") == 0) {
        //     lv_textarea_add_text(data->show, "    ");
        // } else if (strcmp(txt, "Caps") == 0) {
        //     s_flag = !s_flag;
        //     lv_btnmatrix_set_map(kb, s_flag ? kb_map_upper : kb_map_lower);
        // } else if (strcmp(txt, LV_SYMBOL_OK) != 0) {
        //     // 写入并关闭
        //     lv_label_set_text(data->label, lv_textarea_get_text(data->show));
        //     lv_obj_t *popup = lv_obj_get_parent(kb); 
        //     lv_obj_del(popup);
        //     lv_textarea_add_text(data->show, txt);
        // } else{
        //     std::cout << "===========5" << std::endl;
        //     lv_textarea_add_text(data->show, txt);
        // }
    }

}


static void candidate_click_cb(lv_event_t *e)
{
    lv_obj_t *btn = lv_event_get_target(e);
    lv_obj_t *label = (lv_obj_t*)lv_event_get_user_data(e);
    const char *selected_word = lv_label_get_text(btn);

    // 设置 label 文本
    lv_label_set_text(label, selected_word);

    // 清空拼音输入
    // lv_textarea_set_text(ctx->ta_hidden, "");
    // lv_ime_pinyin_set_text(ctx->ime, "");
}

// label 点击事件
static void task_label_event_cb(lv_event_t *e) {
    lv_obj_t *label = lv_event_get_target(e);
    const char *txt = lv_label_get_text(label);

    user_data *data = (user_data*)lv_event_get_user_data(e);
    // 创建一个模态窗口（背景可选透明）
    lv_obj_t *popup = lv_obj_create(lv_scr_act());
    lv_obj_set_size(popup, 400, 300);
    lv_obj_center(popup);
    lv_obj_add_flag(popup, LV_OBJ_FLAG_CLICKABLE);
    lv_obj_set_style_bg_opa(popup, LV_OPA_90, 0);

    // 创建拼音输入法
    lv_obj_t * pinyin_ime = lv_ime_pinyin_create(popup);
   //  lv_obj_set_style_text_font(pinyin_ime, &lv_font_montserrat_14, 0);
    lv_obj_set_style_text_font(pinyin_ime, &lv_font_simsun_16_cjk, 0);

    // 输入框
    lv_obj_t *ta = lv_textarea_create(popup);
    lv_obj_set_style_text_font(ta, &lv_font_simsun_16_cjk, 0);
    lv_textarea_set_one_line(ta, true); // 单行输入
    lv_textarea_add_text(ta, txt);
    lv_obj_set_size(ta, 200, 50);
    lv_obj_align(ta, LV_ALIGN_TOP_MID, 0, 0);
    
    data->show = ta;
    // 键盘
    // lv_obj_t *kb = lv_btnmatrix_create(popup);
    lv_obj_t *kb = lv_keyboard_create(popup);
    
    lv_ime_pinyin_set_keyboard(pinyin_ime, kb);
//     lv_keyboard_set_textarea(kb, ta);
    lv_btnmatrix_set_map(kb, kb_map_lower);
    lv_obj_set_size(kb, 300, 150);
    lv_obj_align(kb, LV_ALIGN_BOTTOM_MID, 0 ,0);
    // lv_obj_del_event_cb(kb, kb_event_cb, LV_EVENT_VALUE_CHANGED, (void*)data);
    lv_obj_remove_event_cb(kb, lv_keyboard_def_event_cb);
    lv_obj_add_event_cb(kb, kb_event_cb, LV_EVENT_VALUE_CHANGED, (void*)data);
    
    // 候选区
    lv_obj_t * cand_panel = lv_ime_pinyin_get_cand_panel(pinyin_ime);
    lv_obj_set_size(cand_panel, LV_PCT(100), LV_PCT(10));
    lv_obj_align_to(cand_panel, kb, LV_ALIGN_OUT_TOP_MID, 0, 0);

    uint32_t cnt = lv_obj_get_child_cnt(cand_panel);
    for (uint32_t i = 0; i < cnt; i++) {
        lv_obj_t *btn = lv_obj_get_child(cand_panel, i);
        lv_obj_add_event_cb(btn, candidate_click_cb, LV_EVENT_CLICKED, label);
    }
}

// 主函数
void lv_ex_btnmatrix_data() {
    user_data *data = new user_data; 
    // 创建一些任务 label 模拟点击项
    lv_obj_t *label = lv_label_create(lv_scr_act());
    lv_obj_set_size(label, 200, 100);
    lv_obj_set_style_border_width(label, 2, LV_PART_MAIN);
    lv_obj_set_style_text_font(label, &lv_font_simsun_16_cjk, 0);

    lv_obj_align(label, LV_ALIGN_TOP_MID, 0, 0);
    lv_label_set_text(label, "");

    data->label = label;
    lv_obj_add_flag(label, LV_OBJ_FLAG_CLICKABLE); // 可点击
    lv_obj_add_event_cb(label, task_label_event_cb, LV_EVENT_CLICKED, (void*)data);
}

