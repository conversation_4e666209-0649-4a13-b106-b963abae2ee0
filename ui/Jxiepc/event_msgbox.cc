#include "event.h"

static void event_cb(lv_event_t * e)
{
        lv_obj_t * obj = lv_event_get_current_target(e);
    LV_LOG_USER("Button %s clicked", lv_msgbox_get_active_btn_text(obj));
}

static void event_btn_cb(lv_event_t * e)
{
    // lv_obj_t * obj = lv_event_get_current_target(e);
    // LV_LOG_USER("Button %s clicked", lv_msgbox_get_active_btn_text(obj));
    static const char * btns[] = {"Apply", "Close", ""};

    lv_obj_t * mbox1 = lv_msgbox_create(NULL, "Hello", "This is a message box with two buttons.", btns, true);
    lv_obj_add_event_cb(mbox1, event_cb, LV_EVENT_VALUE_CHANGED, NULL);
    lv_obj_center(mbox1);
}


void lv_event_msgbox()
{
    lv_obj_t * page = lv_obj_create(lv_scr_act()); 
    lv_obj_set_size(page, 200, 200);
    lv_obj_t * btn = lv_btn_create(page); 
    lv_obj_add_event_cb(
        btn, event_btn_cb, LV_EVENT_CLICKED, NULL);
    
    return;
}


