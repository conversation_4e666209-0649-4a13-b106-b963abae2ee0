#include "lib/src/core/lv_obj.h"
#include "lib/src/widgets/lv_dropdown.h"
#include "lvgl/src/misc/lv_color.h"
#include "util.h"

static void event_handler(lv_event_t * e)
{
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t * obj = lv_event_get_target(e);
    if(code == LV_EVENT_VALUE_CHANGED) {
        char buf[32];
        lv_dropdown_get_selected_str(obj, buf, sizeof(buf));
        LV_LOG_USER("Option: %s", buf);
    }
}

void test_dropdown(void)
{

    /*Create a normal drop down list*/
    lv_obj_t * dd = lv_dropdown_create(lv_scr_act());
    lv_dropdown_set_options(dd, "Apple\n"
                            "Banana\n"
                            "Orange\n"
                            "Cherry\n"
                            "Grape\n"
                            "Raspberry\n"
                            "Melon\n"
                            "Orange\n"
                            "Lemon\n"
                            "Nuts");

    lv_obj_align(dd, LV_ALIGN_TOP_MID, 0, 20);
    // lv_obj_set_style_max_height(dd, 80, LV_PART_MAIN);
    lv_obj_t * list = lv_dropdown_get_list(dd);
    lv_obj_remove_style(list, NULL, LV_PART_SELECTED); // 移除默认选中项样式
    lv_obj_set_style_text_color(dd, lv_palette_main(LV_PALETTE_BLUE), LV_PART_MAIN);
    // lv_obj_set_style_bg_color(list, lv_palette_main(LV_PALETTE_GREEN), LV_PART_MAIN);
    lv_obj_set_style_bg_color(list, lv_palette_main(LV_PALETTE_GREEN), LV_PART_SELECTED);
    lv_dropdown_set_selected_highlight(dd, false);
    lv_obj_add_event_cb(dd, event_handler, LV_EVENT_ALL, NULL);

        // 配置下拉列表
    //     dd = lv_dropdown_create(lv_scr_act());
    //     lv_obj_set_size(dd, 40, 40);
    //     lv_dropdown_set_options(dd, "bx\nby\nsx\nsy\nreset");
    //     // lv_dropdown_set_selected(widget->dropdown, );
    //     lv_dropdown_set_text(dd, ""); // 将显示文本设置为空
    //     lv_obj_set_style_bg_color(dd, lv_color_black(), LV_PART_MAIN);
    //     lv_obj_set_style_bg_opa(dd, LV_OPA_50, LV_PART_MAIN);
    //     lv_obj_set_style_bg_opa(dd, LV_OPA_50, LV_PART_SELECTED);
    //     lv_dropdown_set_symbol(dd, LV_SYMBOL_SETTINGS);
    //     lv_obj_add_event_cb(
    //         dd, event_handler, LV_EVENT_VALUE_CHANGED, NULL);
    //     lv_dropdown_set_selected_highlight(dd, false);
    
    //     lv_obj_t *dp_list = lv_dropdown_get_list(dd);
    //     lv_obj_set_size(dp_list, 40, 40);
    //     lv_obj_set_style_bg_color(dp_list, lv_color_black(), LV_PART_MAIN);
    //     lv_obj_set_style_bg_opa(dp_list, LV_OPA_50, LV_PART_MAIN);
    //     lv_obj_set_style_bg_opa(dp_list, LV_OPA_50, LV_PART_SELECTED);
}
