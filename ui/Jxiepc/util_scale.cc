#include "util.h"
#include <math.h>
#include <stdio.h>

// 弧形刻度参数结构体
typedef struct {
    lv_point_t center;          // 弧形中心点
    lv_coord_t radius;          // 弧形半径
    int16_t start_angle;        // 起始角度（度）
    int16_t end_angle;          // 结束角度（度）
    uint16_t major_tick_count;  // 主刻度数量
    uint16_t minor_tick_count;  // 次刻度数量（每个主刻度间隔内）
    lv_coord_t major_tick_len;  // 主刻度长度
    lv_coord_t minor_tick_len;  // 次刻度长度
    lv_coord_t tick_width;      // 刻度线宽度
    lv_color_t major_color;     // 主刻度颜色
    lv_color_t minor_color;     // 次刻度颜色
} arc_scale_config_t;

// 计算弧形上指定角度的点坐标
static lv_point_t calculate_arc_point(lv_point_t center, lv_coord_t radius, int16_t angle) {
    lv_point_t point;
    float rad = (float)angle * M_PI / 180.0f;
    point.x = center.x + (lv_coord_t)(radius * cosf(rad));
    point.y = center.y + (lv_coord_t)(radius * sinf(rad));
    return point;
}

// 使用 line 绘制弧形刻度的事件回调
static void arc_scale_line_event_cb(lv_event_t *e) {
    lv_obj_t *obj = lv_event_get_target(e);
    lv_draw_ctx_t *draw_ctx = lv_event_get_draw_ctx(e);
    arc_scale_config_t *config = (arc_scale_config_t *)lv_event_get_user_data(e);

    if (!config) return;

    // 设置主刻度线样式
    lv_draw_line_dsc_t major_line_dsc;
    lv_draw_line_dsc_init(&major_line_dsc);
    major_line_dsc.color = config->major_color;
    major_line_dsc.width = config->tick_width;
    major_line_dsc.opa = LV_OPA_COVER;

    // 设置次刻度线样式
    lv_draw_line_dsc_t minor_line_dsc;
    lv_draw_line_dsc_init(&minor_line_dsc);
    minor_line_dsc.color = config->minor_color;
    minor_line_dsc.width = config->tick_width - 1;
    minor_line_dsc.opa = LV_OPA_COVER;

    // 计算角度步长
    float angle_range = config->end_angle - config->start_angle;
    float major_step = angle_range / (config->major_tick_count - 1);

    // 绘制主刻度
    for (uint16_t i = 0; i < config->major_tick_count; i++) {
        float angle = config->start_angle + i * major_step;

        // 计算刻度线的起点和终点
        lv_point_t outer_point = calculate_arc_point(config->center, config->radius, (int16_t)angle);
        lv_point_t inner_point = calculate_arc_point(config->center,
                                                   config->radius - config->major_tick_len,
                                                   (int16_t)angle);

        // 绘制主刻度线
        lv_draw_line(draw_ctx, &major_line_dsc, &inner_point, &outer_point);

        // 绘制次刻度（除了最后一个主刻度）
        if (i < config->major_tick_count - 1 && config->minor_tick_count > 0) {
            float minor_step = major_step / (config->minor_tick_count + 1);

            for (uint16_t j = 1; j <= config->minor_tick_count; j++) {
                float minor_angle = angle + j * minor_step;

                lv_point_t minor_outer = calculate_arc_point(config->center, config->radius, (int16_t)minor_angle);
                lv_point_t minor_inner = calculate_arc_point(config->center,
                                                           config->radius - config->minor_tick_len,
                                                           (int16_t)minor_angle);

                // 绘制次刻度线
                lv_draw_line(draw_ctx, &minor_line_dsc, &minor_inner, &minor_outer);
            }
        }
    }
}

// 使用 line 方式绘制弧形刻度
void draw_arc_scale_with_line() {
    // 创建容器
    lv_obj_t *container = lv_obj_create(lv_scr_act());
    lv_obj_set_size(container, 400, 400);
    lv_obj_center(container);
    lv_obj_set_style_bg_opa(container, LV_OPA_TRANSP, 0);
    lv_obj_set_style_border_opa(container, LV_OPA_TRANSP, 0);

    // 配置弧形刻度参数
    static arc_scale_config_t config = {
        .center = {200, 200},
        .radius = 150,
        .start_angle = -135,    // 起始角度：左下角
        .end_angle = 45,        // 结束角度：右上角
        .major_tick_count = 10, // 10个主刻度
        .minor_tick_count = 4,  // 每个主刻度间隔内4个次刻度
        .major_tick_len = 20,   // 主刻度长度
        .minor_tick_len = 10,   // 次刻度长度
        .tick_width = 3,        // 刻度线宽度
        .major_color = lv_color_hex(0x000000), // 黑色主刻度
        .minor_color = lv_color_hex(0x666666)  // 灰色次刻度
    };

    // 绘制弧形背景
    lv_obj_t *arc_bg = lv_arc_create(container);
    lv_obj_set_size(arc_bg, config.radius * 2, config.radius * 2);
    lv_obj_center(arc_bg);
    lv_arc_set_angles(arc_bg, config.start_angle, config.end_angle);
    lv_arc_set_bg_angles(arc_bg, config.start_angle, config.end_angle);
    lv_obj_set_style_arc_width(arc_bg, 5, LV_PART_MAIN);
    lv_obj_set_style_arc_color(arc_bg, lv_color_hex(0xCCCCCC), LV_PART_MAIN);
    lv_obj_set_style_arc_opa(arc_bg, LV_OPA_TRANSP, LV_PART_INDICATOR);
    lv_obj_clear_flag(arc_bg, LV_OBJ_FLAG_CLICKABLE);

    // 注册绘制事件回调
    lv_obj_add_event_cb(container, arc_scale_line_event_cb, LV_EVENT_DRAW_MAIN, &config);
}

// 使用 arc 控件绘制弧形刻度的事件回调
static void arc_scale_arc_event_cb(lv_event_t *e) {
    lv_obj_t *arc = lv_event_get_target(e);
    lv_obj_draw_part_dsc_t *dsc = lv_event_get_draw_part_dsc(e);
    arc_scale_config_t *config = (arc_scale_config_t *)lv_event_get_user_data(e);

    if (!config || !dsc) return;

    // 处理弧形背景绘制
    if (dsc->type == LV_ARC_DRAW_PART_BACKGROUND) {
        // 可以在这里自定义弧形背景样式
        if (dsc->arc_dsc) {
            dsc->arc_dsc->width = 8;
            dsc->arc_dsc->color = lv_color_hex(0xE0E0E0);
        }
    }
    // 处理弧形前景绘制
    else if (dsc->type == LV_ARC_DRAW_PART_FOREGROUND) {
        // 可以在这里自定义弧形前景样式
        if (dsc->arc_dsc) {
            dsc->arc_dsc->width = 6;
            dsc->arc_dsc->color = lv_color_hex(0x4CAF50);
        }
    }
}

// 使用 arc 控件绘制弧形刻度（带刻度标记）
void draw_arc_scale_with_arc() {
    // 创建容器
    lv_obj_t *container = lv_obj_create(lv_scr_act());
    lv_obj_set_size(container, 400, 400);
    lv_obj_center(container);
    lv_obj_set_style_bg_opa(container, LV_OPA_TRANSP, 0);
    lv_obj_set_style_border_opa(container, LV_OPA_TRANSP, 0);

    // 配置弧形刻度参数
    static arc_scale_config_t config = {
        .center = {200, 200},
        .radius = 150,
        .start_angle = -135,
        .end_angle = 45,
        .major_tick_count = 10,
        .minor_tick_count = 4,
        .major_tick_len = 25,
        .minor_tick_len = 15,
        .tick_width = 3,
        .major_color = lv_color_hex(0x000000),
        .minor_color = lv_color_hex(0x666666)
    };

    // 创建主弧形
    lv_obj_t *main_arc = lv_arc_create(container);
    lv_obj_set_size(main_arc, config.radius * 2, config.radius * 2);
    lv_obj_center(main_arc);
    lv_arc_set_angles(main_arc, config.start_angle, config.end_angle);
    lv_arc_set_bg_angles(main_arc, config.start_angle, config.end_angle);

    // 设置弧形样式
    lv_obj_set_style_arc_width(main_arc, 8, LV_PART_MAIN);
    lv_obj_set_style_arc_color(main_arc, lv_color_hex(0xE0E0E0), LV_PART_MAIN);
    lv_obj_set_style_arc_width(main_arc, 6, LV_PART_INDICATOR);
    lv_obj_set_style_arc_color(main_arc, lv_color_hex(0x4CAF50), LV_PART_INDICATOR);

    // 隐藏旋钮
    lv_obj_set_style_bg_opa(main_arc, LV_OPA_TRANSP, LV_PART_KNOB);
    lv_obj_set_style_border_opa(main_arc, LV_OPA_TRANSP, LV_PART_KNOB);
    lv_obj_clear_flag(main_arc, LV_OBJ_FLAG_CLICKABLE);

    // 设置弧形值（用于显示进度）
    lv_arc_set_value(main_arc, 60); // 60% 进度

    // 注册绘制事件回调
    lv_obj_add_event_cb(main_arc, arc_scale_arc_event_cb, LV_EVENT_DRAW_PART_BEGIN, &config);

    // 添加刻度线（使用额外的绘制回调）
    lv_obj_add_event_cb(container, arc_scale_line_event_cb, LV_EVENT_DRAW_MAIN, &config);
}



// 高性能方案：使用 meter 控件绘制弧形刻度
void draw_arc_scale_with_meter() {
    // 创建仪表盘
    lv_obj_t *meter = lv_meter_create(lv_scr_act());
    lv_obj_set_size(meter, 300, 300);
    lv_obj_remove_style(meter, NULL, LV_PART_INDICATOR);
    lv_obj_remove_style(meter, NULL, LV_PART_INDICATOR);


    // 添加刻度
    lv_meter_scale_t *scale = lv_meter_add_scale(meter);
    lv_meter_set_scale_ticks(meter, scale, 41, 2, 10, lv_color_hex(0x666666));
    lv_meter_set_scale_major_ticks(meter, scale, 4, 4, 20, lv_color_hex(0x000000), 15);
    lv_meter_set_scale_range(meter, scale, 0, 100, 270, 135); // 270度范围，从135度开始

    // 添加弧形指示器
    lv_meter_indicator_t *indic1 = lv_meter_add_arc(meter, scale, 1, lv_color_hex(0x000000), 0);
    lv_meter_set_indicator_start_value(meter, indic1, 0);
    lv_meter_set_indicator_end_value(meter, indic1, 60);

    // 添加指针
    // lv_meter_indicator_t *needle = lv_meter_add_needle_line(meter, scale, 4, lv_color_hex(0xFF5722), -10);
    // lv_meter_set_indicator_value(meter, needle, 60);
}

// 性能对比测试函数
void performance_comparison_test() {
    printf("=== 弧形刻度绘制性能对比 ===\n");
    printf("1. Line方式：灵活度高，可完全自定义，但绘制复杂度较高\n");
    printf("2. Arc方式：结合arc控件，适中的性能和灵活性\n");
    printf("3. Meter方式：LVGL内置优化，性能最佳，但自定义程度有限\n");
}

void test_line_scale() {
    draw_arc_scale_with_line();
}

void test_arc_scale() {
    draw_arc_scale_with_arc();
}

void test_meter_scale() {
    draw_arc_scale_with_meter();
}

void test_scale() {
    // 可以选择不同的实现方式进行测试
    // test_line_scale();    // 使用 line 绘制
    // test_arc_scale();     // 使用 arc 绘制
    test_meter_scale();      // 使用 meter 绘制（推荐）

    // performance_comparison_test();
}