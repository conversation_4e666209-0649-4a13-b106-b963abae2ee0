#include "lvgl/src/core/lv_obj.h"
#include "lvgl/src/misc/lv_color.h"
#include "lvgl/src/widgets/lv_arc.h"
#include "lvgl/src/widgets/lv_bar.h"
#include "util.h"
#include <SDL_pixels.h>


static void draw_line(lv_obj_t * canvas)
{
    // 定义线的描述符
    lv_draw_line_dsc_t line_dsc;
    lv_draw_line_dsc_init(&line_dsc);
    line_dsc.color = lv_palette_main(LV_PALETTE_BLUE);
    line_dsc.width = 4;
    line_dsc.round_end = 1;
    line_dsc.round_start = 1;

    // 定义线的端点
    lv_point_t points[] = {{10, 20}, {80, 90}};

    // 在画布上绘制直线
    lv_canvas_draw_line(canvas, points, 2, &line_dsc);
}

static void draw_sector(lv_obj_t * canvas)
{
    // 定义圆弧（扇形）的描述符
    lv_draw_arc_dsc_t arc_dsc;
    lv_draw_arc_dsc_init(&arc_dsc);
    arc_dsc.color = lv_palette_main(LV_PALETTE_GREEN);
    arc_dsc.width = 10; // 扇形的宽度（从圆心到边缘）

    // 在画布上绘制扇形（例如，从 30 度到 120 度）
    lv_canvas_draw_arc(canvas, 150, 80, 50, 30, 120, &arc_dsc);
}

static void draw_circle(lv_obj_t * canvas){
    // 定义圆弧的描述符
    lv_draw_arc_dsc_t arc_dsc;
    lv_draw_arc_dsc_init(&arc_dsc);
    arc_dsc.color = lv_palette_main(LV_PALETTE_RED);
    arc_dsc.width = 5;

    // 在画布上绘制一个完整的圆弧（0-360度）
    lv_canvas_draw_arc(canvas, 50, 50, 30, 0, 360, &arc_dsc);
}



// 示例调用
// void test_ring(void) {
//     lv_obj_t* obj = lv_arc_create(lv_scr_act());
//     lv_obj_set_size(obj, 200, 200);
//     lv_obj_align(obj, LV_ALIGN_CENTER, 0, 0);

//     lv_obj_remove_style(obj, NULL, LV_PART_KNOB);   /*Be sure the knob is not displayed*/

//     // lv_arc_set_angles(obj, 0, 180);
//     lv_arc_set_bg_angles(obj, 0, 360);
//     lv_bar_set_mode(obj, LV_BAR_MODE_RANGE);
// }

void draw_event(lv_event_t * e) {

}

void test_ring(void) {
    // 定义 Canvas 缓冲区（假设分辨率为 200x200）
    static lv_color_t cbuf[LV_CANVAS_BUF_SIZE_TRUE_COLOR(200, 200)];
    lv_obj_t *canvas = lv_canvas_create(lv_scr_act());
    lv_canvas_set_buffer(canvas, cbuf, 200, 200, LV_IMG_CF_TRUE_COLOR);

    // 设置 Canvas 位置
    lv_obj_center(canvas);

    // 清空 Canvas（透明背景）
    lv_canvas_fill_bg(canvas, lv_color_hex(0x000000), LV_OPA_TRANSP);

    // 定义弧线路径点
    #define POINT_CNT 100
    lv_point_t points[POINT_CNT];
    float center_x = 100, center_y = 100; // 中心点
    float radius = 50; // 基础半径
    float angle_step = 360.0 / POINT_CNT; // 角度步进

    for (int i = 0; i < POINT_CNT; i++) {
        float angle = i * angle_step * M_PI / 180.0; // 弧度
        // 不规则效果：半径随角度变化（正弦波调制）
        float modulated_radius = radius + 10 * sinf(angle * 4); // 振幅 10，频率 4
        points[i].x = center_x + modulated_radius * cosf(angle);
        points[i].y = center_y + modulated_radius * sinf(angle);
    }

    // 绘制路径（连接点形成弧线）
    lv_draw_line_dsc_t line_dsc;
    lv_draw_line_dsc_init(&line_dsc);
    line_dsc.color = lv_color_hex(0x00FF00); // 绿色
    line_dsc.width = 3; // 初始线宽

    // for (int i = 0; i < POINT_CNT - 1; i++) {
    //     // 可选：动态调整线宽，增加不规则效果
    //     line_dsc.width = 3 + (int)(2 * sinf(i * 0.1)); // 线宽随索引变化
    //     lv_canvas_draw_line(canvas, points, 2, &line_dsc);
    // }

    // 可选：闭合弧线
    line_dsc.width = 3;
    lv_canvas_draw_line(canvas, points, POINT_CNT, &line_dsc);
}