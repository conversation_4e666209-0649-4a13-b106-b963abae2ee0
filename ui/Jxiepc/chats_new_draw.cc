#include "chart.h"


void chart_new_draw(void) {
    // 创建可滚动的父容器
    lv_obj_t *parent = lv_obj_create(lv_scr_act());
    lv_obj_set_size(parent, 200, 150);
    lv_obj_center(parent);
    lv_obj_set_scrollbar_mode(parent, LV_SCROLLBAR_MODE_AUTO);
    lv_obj_add_flag(parent, LV_OBJ_FLAG_SCROLLABLE | LV_OBJ_FLAG_SCROLL_MOMENTUM | LV_OBJ_FLAG_SCROLL_ELASTIC);
    lv_obj_set_scroll_dir(parent, LV_DIR_ALL);

    // 创建曲线图
    lv_obj_t *chart = lv_chart_create(parent);
    lv_obj_set_size(chart, 300, 200); // 大于父容器以触发滚动
    lv_chart_set_type(chart, LV_CHART_TYPE_LINE);
    lv_chart_set_point_count(chart, 100);
    lv_chart_set_range(chart, LV_CHART_AXIS_PRIMARY_Y, 0, 100);
    lv_chart_set_div_line_count(chart, 5, 5);
    lv_chart_set_update_mode(chart, LV_CHART_UPDATE_MODE_SHIFT);

    // 添加数据系列
    lv_chart_series_t *ser = lv_chart_add_series(chart, lv_palette_main(LV_PALETTE_RED), LV_CHART_AXIS_PRIMARY_Y);

    // 动态更新数据
    static int data_value = 0;
    lv_timer_t *timer = lv_timer_create([](lv_timer_t *timer) {
        lv_obj_t *chart = (lv_obj_t *)timer->user_data;
        lv_chart_series_t *ser = lv_chart_get_series_next(chart, NULL);
        lv_chart_set_next_value(chart, ser, (data_value++ % 100));
        lv_chart_refresh(chart);
    }, 500, chart);

    // 事件处理以优化拖拽
    lv_obj_add_event_cb(chart, [](lv_event_t *e) {
        lv_event_code_t code = lv_event_get_code(e);
        lv_obj_t *obj = lv_event_get_target(e);
        if (code == LV_EVENT_PRESSED) {
            lv_obj_scroll_by(obj->parent, 0, 0, LV_ANIM_OFF);
        }
    }, LV_EVENT_ALL, NULL);
}