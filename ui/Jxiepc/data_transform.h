#include <iostream>

#ifndef DATA_TRANSFORM_H
#define DATA_TRANSFORM_H

#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <stdbool.h>

typedef enum {
    DATA_ALL_TYPE_BOOL = 1,
    DATA_ALL_TYPE_UINT8 = 2,
    DATA_ALL_TYPE_INT8 = 3,
    DATA_ALL_TYPE_UINT16 = 4,
    DATA_ALL_TYPE_INT16 = 5,
    DATA_ALL_TYPE_UINT32 = 6,
    DATA_ALL_TYPE_INT32 = 7,
    DATA_ALL_TYPE_FLOAT = 8,
    DATA_ALL_TYPE_DOUBLE = 9,
    DATA_ALL_TYPE_STRING = 10
} data_all_type_t;

// 转换函数
double to_double(void* data, data_all_type_t type);


#endif
