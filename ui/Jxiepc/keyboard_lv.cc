#include "keyboard.h"

static const char *my_kb_map[] = {
    "1", "2", "3", "\n",
    "4", "5", "6", "\n",
    "7", "8", "9", "\n",
    LV_SYMBOL_KEYBOARD, LV_SYMBOL_BACKSPACE, LV_SYMBOL_NEW_LINE, ""
};

static const lv_btnmatrix_ctrl_t my_kb_ctrl_map[] = {
    LV_BTNMATRIX_CTRL_CLICK_TRIG,  // "1"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,  // "2"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,  // "3"
    0,                             // 换行
    LV_BTNMATRIX_CTRL_CLICK_TRIG,  // "4"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,  // "5"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,  // "6"
    0,                             // 换行
    LV_BTNMATRIX_CTRL_CLICK_TRIG,  // "7"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,  // "8"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,  // "9"
    0,                             // 换行
    LV_BTNMATRIX_CTRL_CLICK_TRIG,  // "Del"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,  // "0"
    LV_BTNMATRIX_CTRL_CLICK_TRIG | LV_BTNMATRIX_CTRL_CHECKABLE, // "OK"
    0                              // 结尾
};

static void kb_event_cb(lv_event_t *e)
{
    // lv_keyboard_def_event_cb(e);
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t *kb = lv_event_get_target(e);
    lv_obj_t *ta = (lv_obj_t *)lv_event_get_user_data(e);

    if(code == LV_EVENT_DRAW_PART_END) {
        lv_obj_draw_part_dsc_t * dsc = lv_event_get_draw_part_dsc(e);
        /*When the button matrix draws the buttons...*/
        if(dsc->class_p == &lv_btnmatrix_class && dsc->type == LV_BTNMATRIX_DRAW_PART_BTN) {
            /*Change the draw descriptor of the 2nd button*/
            if(dsc->id == 1) {
                std::cout << "=====> " << dsc->id  <<lv_btnmatrix_get_selected_btn(kb)  << std::endl;
                dsc->rect_dsc->radius = 0;
                if(lv_btnmatrix_get_selected_btn(kb) == dsc->id)  dsc->rect_dsc->bg_color = lv_palette_darken(LV_PALETTE_BLUE, 3);
                else dsc->rect_dsc->bg_color = lv_palette_main(LV_PALETTE_BLUE);

                dsc->rect_dsc->shadow_width = 10;
                dsc->rect_dsc->shadow_ofs_x = 3;
                dsc->rect_dsc->shadow_ofs_y = 3;
                dsc->label_dsc->color = lv_color_black();
            }
            /*Change the draw descriptor of the 3rd button*/
            else if(dsc->id == 2) {
                dsc->rect_dsc->radius = LV_RADIUS_CIRCLE;
                if(lv_btnmatrix_get_selected_btn(kb) == dsc->id)  dsc->rect_dsc->bg_color = lv_palette_darken(LV_PALETTE_RED, 3);
                else dsc->rect_dsc->bg_color = lv_palette_main(LV_PALETTE_RED);

                dsc->label_dsc->color = lv_color_white();
            }
            else if(dsc->id == 3) {
                dsc->label_dsc->opa = LV_OPA_TRANSP; /*Hide the text if any*/

            }
        }
    }

    if (code == LV_EVENT_VALUE_CHANGED) {
        const char *btn_text = lv_btnmatrix_get_btn_text(kb, lv_btnmatrix_get_selected_btn(kb));
        // lv_keyboard_def_event_cb(e); // 如果没禁用，还是用默认处理逻辑

        // if (strcmp(btn_text, "OK") == 0) {
        //     lv_obj_add_flag(kb, LV_OBJ_FLAG_HIDDEN); // 隐藏键盘
        // }
        if(strcmp(btn_text, "2") == 0) {
            std::cout << "======> " << btn_text << std::endl;
            lv_keyboard_def_event_cb(e);
        }
    }

    // 默认处理（自动写入文本）
}

void lv_ex_btnmatrix_lv()
{
    // 创建文本框
    lv_obj_t *ta = lv_textarea_create(lv_scr_act());
    lv_obj_set_width(ta, 200);
    lv_obj_align(ta, LV_ALIGN_TOP_MID, 0, 20);
    lv_textarea_set_placeholder_text(ta, "请输入内容");

    // 创建键盘
    lv_obj_t *kb = lv_keyboard_create(lv_scr_act());
    lv_keyboard_set_textarea(kb, ta);
    // lv_obj_add_event_cb(kb, kb_event_cb, LV_EVENT_VALUE_CHANGED, ta);
    // lv_obj_remove_event_cb(kb, lv_keyboard_def_event_cb); // 完全移除

    lv_obj_t * pinyin_ime = lv_ime_pinyin_create(lv_scr_act());
   //  lv_obj_set_style_text_font(pinyin_ime, &lv_font_montserrat_14, 0);
    lv_obj_set_style_text_font(pinyin_ime, &lv_font_simsun_16_cjk, 0);
    lv_ime_pinyin_set_keyboard(pinyin_ime, kb);
    lv_keyboard_set_textarea(kb, ta);
    
    // 候选区
    lv_obj_t * cand_panel = lv_ime_pinyin_get_cand_panel(pinyin_ime);
    lv_obj_set_size(cand_panel, LV_PCT(100), LV_PCT(10));
    lv_obj_align_to(cand_panel, kb, LV_ALIGN_OUT_TOP_MID, 0, 0);

    lv_obj_add_event_cb(kb, kb_event_cb, LV_EVENT_ALL,ta);
    lv_obj_set_size(kb, 300, 200);

    // 使用内置“数字键盘”布局
    // lv_keyboard_set_mode(kb, LV_KEYBOARD_MODE_NUMBER);
    lv_keyboard_set_mode(kb, LV_KEYBOARD_MODE_TEXT_UPPER);
    // TODO
    // lv_keyboard_set_map(kb, LV_KEYBOARD_MODE_USER_1, my_kb_map, my_kb_ctrl_map);
    // lv_keyboard_set_mode(kb, LV_KEYBOARD_MODE_USER_1);

    lv_btnmatrix_set_btn_width(kb, 10, 2);        /*Make "Action1" twice as wide as "Action2"*/

    // 自定义样式（圆角、颜色等）
    // lv_obj_set_style_radius(kb, 8, 0);
    // lv_obj_set_style_bg_color(kb, lv_color_hex(0xeeeeee), 0);
    // lv_obj_set_style_text_font(kb, &lv_font_montserrat_14, LV_PART_ITEMS);
    // lv_obj_set_style_pad_all(kb, 4, 0);
}

