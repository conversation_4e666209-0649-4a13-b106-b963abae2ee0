#include "keyboard.h"

#define MY_SYMBOL_UP "\xEF\x95\x92"

static void my_kb_event_cb(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t *btnm = lv_event_get_target(e);
    const char *txt = lv_btnmatrix_get_btn_text(btnm, lv_btnmatrix_get_selected_btn(btnm));
    lv_obj_t *ta = (lv_obj_t *)lv_event_get_user_data(e);

    if(code == LV_EVENT_VALUE_CHANGED) {
        if(strcmp(txt, "Del") == 0) {
            lv_textarea_del_char(ta);
        }
        else if(strcmp(txt, "Enter") == 0) {
            lv_textarea_add_text(ta, "\n");
        }
        else {
            lv_textarea_add_text(ta, txt);
        }
    }
}

static const char *number1[] = {
    "1","2","3","4","5","6","7","8","9","0","\n", 
    "!","@","$","%","^","&","*","(",")","-","\n",
    "=","[","]","{","}","|","\\",":",",",";","\n",
    ",",".","<",">","/","?"," "," "," "," ",LV_SYMBOL_BACKSPACE,"\n"
    " "," "," "," "," "," "," "," "," "," ", "\n",
    "abc", "Space","Enter","\n",
    ""};



static const char *lower_letter[] = {
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
    "0",
    "\n",
    "q",
    "w",
    "e",
    "r",
    "t",
    "y",
    "u",
    "i",
    "o",
    "p",
    "\n",
    "a",
    "s",
    "d",
    "f",
    "g",
    "h",
    "j",
    "k",
    "l",
    "\n",
    "Caps",
    "z",
    "x",
    "c",
    "v",
    "b",
    "n",
    "m",
    LV_SYMBOL_BACKSPACE,
    "\n",
    "123",
    "Space",
    "Enter",
    "\n",
    "",
};

static const lv_btnmatrix_ctrl_t lower_letter_ctrl[] = {
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "1"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "2"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "3"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "4"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "5"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "6"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "7"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "8"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "9"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "0"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "q"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "w"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "e"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "r"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "t"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "y"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "u"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "i"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "o"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "p"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // ""
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "a"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "s"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "d"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "f"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "g"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "h"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "j"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "k"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "l"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // ""
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "Caps"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "z"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "x"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "c"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "v"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "b"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "n"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "m"
    5 | LV_BTNMATRIX_CTRL_CLICK_TRIG, // "backspace"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "123"
};


static const char *upper_letter[] = {
    "1","2","3","4","5","6","7","8","9","0","\n", 
    "Q","W","E","R", "T","Y","U","I","O","P","\n", 
    " ","A","S","D","F","G","H","J","K","L"," ","\n",
    "Caps","Z","X","C","V","B","N","M",LV_SYMBOL_BACKSPACE, "\n",
    "123",  "space", "enter",""
};


static const lv_btnmatrix_ctrl_t upper_letter_ctrl[] = {
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "1"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "2"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "3"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "4"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "5"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "6"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "7"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "8"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "9"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "0"
    0,                            // 换行
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "q"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "w"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "e"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "r"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "t"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "y"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "u"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "i"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "o"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "p"
    0,                            // 换行
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "a"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "s"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "d"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "f"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "g"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "h"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "j"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "k"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "l"
    0,                            // 换行
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "z"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "x"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "c"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "v"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "b"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "h"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "n"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "m"
    0,                            // 结尾
    0                             // 结尾
};

static const char *number_letter[] = {
    "7", "8", "9", LV_SYMBOL_BACKSPACE,"\n",
    "4", "5", "6", "+/-","\n",
    "1", "2", "3", "Clear","\n",
    ".", "0", "Enter", "\n", ""

};


static const lv_btnmatrix_ctrl_t number_ctrl[] = {
    LV_BTNMATRIX_CTRL_CLICK_TRIG,                                   // "7"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,                                   // "8"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,                                   // "9"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,                                   // BACKSPACE
    LV_BTNMATRIX_CTRL_CLICK_TRIG,                                   // "4"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,                                   // "5"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,                                   // "6"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,                                   // "+/-"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,                                   // "1"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,                                   // "2"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,                                   // "3"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,                                   // clear
    LV_BTNMATRIX_CTRL_CLICK_TRIG,                                   // "."
    LV_BTNMATRIX_CTRL_CLICK_TRIG,                                   // "0"
    2 | LV_BTNMATRIX_CTRL_CLICK_TRIG | LV_BTNMATRIX_CTRL_CHECKABLE, // "Enter"
};

//static const lv_btnmatrix_ctrl_t number_ctrl[] = {
//    LV_BTNMATRIX_CTRL_CLICK_TRIG,                               // "1"
//    LV_BTNMATRIX_CTRL_CLICK_TRIG,                               // "2"
//    LV_BTNMATRIX_CTRL_CLICK_TRIG,                               // "3"
//    0,                                                          // 换行
//    LV_BTNMATRIX_CTRL_CLICK_TRIG,                               // "4"
//    LV_BTNMATRIX_CTRL_CLICK_TRIG,                               // "5"
//    LV_BTNMATRIX_CTRL_CLICK_TRIG,                               // "6"
//    0,                                                          // 换行
//    LV_BTNMATRIX_CTRL_CLICK_TRIG,                               // "7"
//    LV_BTNMATRIX_CTRL_CLICK_TRIG,                               // "8"
//    LV_BTNMATRIX_CTRL_CLICK_TRIG,                               // "9"
//    0,                                                          // 换行
//    1,                               // "Del"
//    1,                               // "0"
//    2|LV_BTNMATRIX_CTRL_CLICK_TRIG | LV_BTNMATRIX_CTRL_CHECKABLE, // "OK"
//    0                                                           // 结尾
//};



static const char *symbol_letter[] = {
    "1","2","3","4","5","6","7","8","9","0","\n", 
    "-","/",":",";", "(",")","¥","@","“","”","\n", 
    "#+=","。","，","、","？","！",".",LV_SYMBOL_BACKSPACE,"\n",
    "123",  "space", "enter",""
};



static const lv_btnmatrix_ctrl_t number1_ctrl[] = {
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "1"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "2"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "3"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "4"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "5"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "6"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "7"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "8"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "9"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "0"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "-"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "/"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // ":"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // ";"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "("
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // ")"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "¥"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "@"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "“"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "”"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "\n"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "#+="
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "。"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "，"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "、"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "？"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "！"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "."
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "BACKSPACE"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "Enter"
};


static const char *symbol[] = {
    "【",    "】", "{",  "}",  "#", "%",  "^",
    "*",     "+",  "=",  "\n", "_", "——", "\\",
    "|",     "~",  "《", "》", "$", "&",  "·",
    "\n","123",   "…",  ",",  " ",  "?", "‘",  LV_SYMBOL_BACKSPACE,
    "Enter", ""};

static const lv_btnmatrix_ctrl_t symbol_ctrl[] = {
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "【"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "】"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "{"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "}"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "#"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "%"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "^"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "*"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "+"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "="
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "_"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "——"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "\\"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "|"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "~"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "《"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "》"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "$"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "&"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "·"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "123"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "…"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // ","
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // " "
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "?"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "‘"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "BACKSPACE"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "Enter"
};

void lv_ex_btnmatrix_custom() {
    LV_FONT_DECLARE(lv_font_Material_18);
    lv_obj_t *ta = lv_textarea_create(lv_scr_act());
    lv_obj_set_width(ta, 200);
    
    //lv_obj_t *btnm = lv_btnmatrix_create(lv_scr_act());
    lv_obj_t *btnm = lv_keyboard_create(lv_scr_act());
    // lv_obj_set_style_text_font(btnm, &lv_font_simsun_16_cjk, 0);
//     lv_obj_set_style_text_font(btnm, &lv_font_Material_18, 0);
    // lv_obj_set_size(btnm, 500, 350);
    lv_obj_set_size(btnm, 800, 400);
    lv_keyboard_set_mode(btnm, LV_KEYBOARD_MODE_USER_1);
    //lv_btnmatrix_set_map(btnm, number_letter);
    // lv_keyboard_set_map(btnm, LV_KEYBOARD_MODE_USER_1, lower_letter, lower_letter_ctrl);
    // lv_keyboard_set_map(btnm, LV_KEYBOARD_MODE_USER_1, upper_letter, lower_letter_ctrl );
    // lv_keyboard_set_map(btnm, LV_KEYBOARD_MODE_USER_1, number_letter, number_ctrl);
    lv_keyboard_set_map(btnm, LV_KEYBOARD_MODE_USER_1, number1, number1_ctrl);
    // lv_keyboard_set_map(btnm, LV_KEYBOARD_MODE_USER_1, symbol, symbol_ctrl);
    lv_obj_align(btnm, LV_ALIGN_BOTTOM_MID, 0, 0);
    
    // lv_obj_add_event_cb(btnm, my_kb_event_cb, LV_EVENT_VALUE_CHANGED, ta);
}
