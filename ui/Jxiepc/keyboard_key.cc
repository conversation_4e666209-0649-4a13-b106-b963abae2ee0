#include "keyboard.h"


// 按钮事件处理函数
void btnm_event_cb(lv_event_t * e) {
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t * btnm = lv_event_get_target(e);

    // if(code == LV_EVENT_DRAW_PART_BEGIN) {
    //     lv_obj_draw_part_dsc_t * dsc = lv_event_get_draw_part_dsc(e);

    //     // 修改绘制区域
    //     if(dsc->draw_area) {
    //         // 修改绘制区域为一个新的矩形区域
    //         dsc->draw_area->x1 = 50;  // 左上角 X 坐标
    //         dsc->draw_area->y1 = 100; // 左上角 Y 坐标
    //         dsc->draw_area->x2 = 200; // 右下角 X 坐标
    //         dsc->draw_area->y2 = 150; // 右下角 Y 坐标
    //     }

    //     // 你还可以修改其他坐标点
    //     // if(dsc->p1) {
    //     //     // 假设 p1 是某个点的坐标，修改它
    //     //     dsc->p1->x = 30;
    //     //     dsc->p1->y = 60;
    //     // }
    // }



    if(code == LV_EVENT_VALUE_CHANGED) {
        uint16_t id = lv_btnmatrix_get_selected_btn(btnm);
        const char * txt = lv_btnmatrix_get_btn_text(btnm, id);
        std::cout << "ENTER pressed: button: " << id << " , text: " << txt << std::endl;
    }
}



void lv_ex_btnmatrix_3(lv_indev_t* indev) {
    // 创建按钮矩阵
    lv_obj_t *btnm = lv_btnmatrix_create(lv_scr_act());
    static const char * btn_map[] = { "1", "2", "3", "\n", "4", "5", "6", "" };
    lv_btnmatrix_set_map(btnm, btn_map);

    // 允许键盘控制选择
    lv_obj_add_flag(btnm, LV_OBJ_FLAG_CLICK_FOCUSABLE);
    lv_group_t *g = lv_group_create();     // 创建一个 group
    lv_group_add_obj(g, btnm);             // 将按钮矩阵加入 group
    lv_indev_set_group(indev, g);  // 绑定键盘设备

    // 添加事件回调处理
    lv_obj_add_event_cb(btnm, btnm_event_cb, LV_EVENT_ALL, NULL);

}
