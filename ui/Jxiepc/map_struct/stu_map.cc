
#include "stu_map.h"

person_map_t *map_new(void) { return kh_init_person_map(); }

void map_free(person_map_t *self) { kh_destroy_person_map(self); }

void map_resize(person_map_t *self, uint32_t size)
{
    kh_resize_person_map(self, size);
}

int map_add(person_map_t *const self, uint32_t id, Person *v)
{
    int ret = 0;
    khiter_t k = kh_put_person_map(self, (khint32_t)id, &ret);
    if (ret == 1) {
        // 初始化数据
        kh_val(self, k) = *v;
        return 0;
    } else {
        // std::cout << "add error: " << ret << std::endl;
    }
    return 0;
}

int map_del(person_map_t *const self, uint32_t id)
{
    khiter_t k = kh_get_person_map(self, (khint32_t)id);
    if (k == kh_end(self)) {
        return -1;
    }
    kh_del_person_map(self, k);
    return 0;
}

Person *map_get(const person_map_t *self, uint32_t id)
{
    khiter_t k = kh_get_person_map(self, (khint32_t)id);
    if (k == kh_end(self)) {
        return 0;
    }
    return &kh_val(self, k);
}

bool map_has(const person_map_t *self, uint32_t id)
{
    khiter_t k = kh_get_person_map(self, (khint32_t)id);
    return k != kh_end(self);
}

