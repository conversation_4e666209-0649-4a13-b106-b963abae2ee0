#pragma once

#include "khash.h"
#include <iostream>

#ifndef URQ_LVGL_MAP_H
#define URQ_LVGL_MAP_H
#ifdef __cplusplus
extern "C" {
#endif

#pragma GCC diagnostic push                   // 保存当前警告状态
#pragma GCC diagnostic ignored "-Wconversion" // 忽略 -Wconversion 相关警告

/// @brief 表
KHASH_MAP_INIT_INT(int_map, int)
#pragma GCC diagnostic pop // 恢复之前的警告状态

typedef khash_t(int_map) int_map_t;

int_map_t *map_new(void);

void map_free(int_map_t *self);

void map_resize(int_map_t *self, uint32_t size);

int map_add(int_map_t *const self, uint32_t id, int v);

int map_del(int_map_t *const self, uint32_t id);

int map_get(const int_map_t *self, uint32_t id);

bool map_has(const int_map_t *self, uint32_t id);

#ifdef __cplusplus
}
#endif

#endif // URQ__LVGL__MAP_H

