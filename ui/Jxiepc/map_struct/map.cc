
#include "map.h"

int_map_t *map_new(void) { return kh_init_int_map(); }

void map_free(int_map_t *self) { kh_destroy_int_map(self); }

void map_resize(int_map_t *self, uint32_t size)
{
    kh_resize_int_map(self, size);
}

int map_add(int_map_t *const self, uint32_t id, int v)
{
    int ret = 0;
    khiter_t k = kh_put_int_map(self, (khint32_t)id, &ret);
    std::cout << "add error: " << ret << ":" << kh_val(self, k) << std::endl;
    if (ret == 1) {
        // 初始化数据
        kh_val(self, k) = v;
        return 0;
    } else {
        // std::cout << "add error: " << ret << std::endl;
    }
    return 0;
}

int map_del(int_map_t *const self, uint32_t id)
{
    khiter_t k = kh_get_int_map(self, (khint32_t)id);
    if (k == kh_end(self)) {
        return -1;
    }
    kh_del_int_map(self, k);
    return 0;
}

int map_get(const int_map_t *self, uint32_t id)
{
    khiter_t k = kh_get_int_map(self, (khint32_t)id);
    if (k == kh_end(self)) {
        return 0;
    }
    return kh_val(self, k);
}

bool map_has(const int_map_t *self, uint32_t id)
{
    khiter_t k = kh_get_int_map(self, (khint32_t)id);
    return k != kh_end(self);
}

